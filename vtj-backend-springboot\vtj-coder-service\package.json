{"name": "vtj-coder-service", "version": "1.0.0", "description": "VTJ 代码转换微服务 - 处理 DSL ↔ Vue 转换", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "dependencies": {"@vtj/coder": "latest", "@vtj/parser": "latest", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["vtj", "lowcode", "dsl", "vue", "coder", "parser"], "author": "VTJ Team", "license": "MIT"}