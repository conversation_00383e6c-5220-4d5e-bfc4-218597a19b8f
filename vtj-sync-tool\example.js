#!/usr/bin/env node

/**
 * VTJ同步工具使用示例
 * 演示如何使用VTJSyncTool类
 */

const VTJSyncTool = require('./vtj-sync');
const chalk = require('chalk');

async function example() {
  console.log(chalk.blue.bold('🚀 VTJ同步工具使用示例\n'));

  // 创建同步工具实例
  const syncTool = new VTJSyncTool('./vtj-config.json');

  try {
    // 1. 显示状态信息
    console.log(chalk.cyan('1. 显示状态信息'));
    await syncTool.showStatus();
    console.log('\n');

    // 2. 拉取单个页面
    console.log(chalk.cyan('2. 拉取单个页面示例'));
    // await syncTool.pullPage('HomePage');
    console.log(chalk.gray('   (已注释，取消注释以执行)'));
    console.log('\n');

    // 3. 拉取所有页面
    console.log(chalk.cyan('3. 拉取所有页面示例'));
    // await syncTool.pullAllPages();
    console.log(chalk.gray('   (已注释，取消注释以执行)'));
    console.log('\n');

    // 4. 推送单个页面
    console.log(chalk.cyan('4. 推送单个页面示例'));
    // await syncTool.pushPage('./src/pages/HomePage.vue');
    console.log(chalk.gray('   (已注释，取消注释以执行)'));
    console.log('\n');

    // 5. 推送所有页面
    console.log(chalk.cyan('5. 推送所有页面示例'));
    // await syncTool.pushAllPages();
    console.log(chalk.gray('   (已注释，取消注释以执行)'));
    console.log('\n');

    // 6. 开始监听（这会阻塞程序）
    console.log(chalk.cyan('6. 开始监听文件变化'));
    console.log(chalk.gray('   取消注释下面的代码以启动监听模式：'));
    console.log(chalk.gray('   // syncTool.startWatch();'));
    console.log('\n');

    console.log(chalk.green('✅ 示例运行完成！'));
    console.log(chalk.blue('💡 提示：'));
    console.log('   - 取消注释相应代码以执行实际操作');
    console.log('   - 确保VTJ后端服务正在运行');
    console.log('   - 修改vtj-config.json中的配置');

  } catch (error) {
    console.error(chalk.red('❌ 示例运行失败:'), error.message);
  }
}

// 运行示例
if (require.main === module) {
  example();
}

module.exports = example;
