package com.vtj.backend.service;

import com.vtj.backend.dto.UserChatDto;

import java.util.List;
import java.util.Map;

/**
 * AI对话服务接口
 */
public interface ChatService {

    /**
     * 获取对话列表
     */
    List<Map<String, Object>> getChats(String topicId);

    /**
     * 发送对话
     */
    String postChat(UserChatDto dto);

    /**
     * 保存对话
     */
    void saveChat(String chatId, String topicId, String role, String content, Integer tokens);

    /**
     * 删除话题的所有对话
     */
    void deleteChatsByTopicId(String topicId);
}
