package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI话题实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "topics")
public class Topic extends BaseEntity {

    /**
     * 话题ID
     */
    @Column(name = "topic_id", nullable = false, unique = true, length = 50)
    private String topicId;

    /**
     * 话题标题
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 类型: text/image/json
     */
    @Column(name = "type", length = 20)
    private String type = "text";

    /**
     * AI模型
     */
    @Column(name = "model", length = 50)
    private String model;

    /**
     * 文件URL(图片/JSON)
     */
    @Column(name = "file_url", length = 500)
    private String fileUrl;

    /**
     * 提示词
     */
    @Column(name = "prompt", columnDefinition = "TEXT")
    private String prompt;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;
}
