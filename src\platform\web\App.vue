<template>
  <ElConfigProvider :locale="zhCn">
    <Suspense>
      <RouterView :key="route.fullPath"></RouterView>
    </Suspense>
  </ElConfigProvider>
</template>
<script setup lang="ts">
import { Suspense } from 'vue';
import { RouterView, useRoute } from 'vue-router';
import { ElConfigProvider } from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
const route = useRoute();
</script>
