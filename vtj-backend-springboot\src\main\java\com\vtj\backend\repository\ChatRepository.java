package com.vtj.backend.repository;

import com.vtj.backend.entity.Chat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI对话记录数据访问层
 */
@Repository
public interface ChatRepository extends JpaRepository<Chat, Long> {

    /**
     * 根据对话ID查找
     */
    Optional<Chat> findByChatId(String chatId);

    /**
     * 根据话题ID查找对话列表
     */
    List<Chat> findByTopicIdOrderByCreatedAtAsc(String topicId);

    /**
     * 根据话题ID删除所有对话
     */
    void deleteByTopicId(String topicId);

    /**
     * 根据话题ID和角色查找对话
     */
    List<Chat> findByTopicIdAndRole(String topicId, String role);
}
