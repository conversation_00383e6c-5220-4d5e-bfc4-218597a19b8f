<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>VTJ CORS 测试页面</h1>
    
    <div class="test-section">
        <h2>测试配置</h2>
        <p>前端地址: http://localhost:9527</p>
        <p>后端地址: http://localhost:8080/api</p>
    </div>

    <div class="test-section">
        <h2>测试用例</h2>
        
        <div>
            <h3>1. 测试获取应用列表</h3>
            <button onclick="testGetApps()">测试 GET /apps</button>
            <div id="apps-result"></div>
        </div>

        <div>
            <h3>2. 测试创建应用</h3>
            <button onclick="testCreateApp()">测试 POST /apps</button>
            <div id="create-app-result"></div>
        </div>

        <div>
            <h3>3. 测试获取用户信息</h3>
            <button onclick="testGetUser()">测试 GET /open/user</button>
            <div id="user-result"></div>
        </div>

        <div>
            <h3>4. 测试数据上报</h3>
            <button onclick="testReport()">测试 POST /open/report</button>
            <div id="report-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        function showResult(elementId, success, message, data) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <p class="${success ? 'success' : 'error'}">${success ? '✓' : '✗'} ${message}</p>
                ${data ? '<pre>' + JSON.stringify(data, null, 2) + '</pre>' : ''}
            `;
        }

        async function testGetApps() {
            try {
                const response = await fetch(`${API_BASE}/apps/action/find-my-apps?page=1&limit=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                showResult('apps-result', response.ok, `状态码: ${response.status}`, data);
            } catch (error) {
                showResult('apps-result', false, `错误: ${error.message}`);
            }
        }

        async function testCreateApp() {
            try {
                const response = await fetch(`${API_BASE}/apps`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        name: 'test-app-' + Date.now(),
                        label: '测试应用',
                        platform: 'web'
                    })
                });
                
                const data = await response.json();
                showResult('create-app-result', response.ok, `状态码: ${response.status}`, data);
            } catch (error) {
                showResult('create-app-result', false, `错误: ${error.message}`);
            }
        }

        async function testGetUser() {
            try {
                const response = await fetch(`${API_BASE}/open/user/test-token`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                const data = await response.json();
                showResult('user-result', response.ok, `状态码: ${response.status}`, data);
            } catch (error) {
                showResult('user-result', false, `错误: ${error.message}`);
            }
        }

        async function testReport() {
            try {
                const testData = { event: 'test', timestamp: Date.now() };
                const encodedData = btoa(JSON.stringify(testData));
                
                const response = await fetch(`${API_BASE}/open/report`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ data: encodedData })
                });
                
                const data = await response.json();
                showResult('report-result', response.ok, `状态码: ${response.status}`, data);
            } catch (error) {
                showResult('report-result', false, `错误: ${error.message}`);
            }
        }
    </script>
</body>
</html> 