# VTJ 低代码平台后端部署指南

## 🎯 部署概览

本项目包含两个服务：
- **Spring Boot 服务**：主要的 API 服务（端口 8080）
- **Node.js 微服务**：代码转换服务（端口 3001）

## 📋 环境要求

### 基础环境
- **Java**: JDK 17 或更高版本
- **Node.js**: 16.x 或更高版本
- **MySQL**: 8.0 或更高版本
- **Maven**: 3.6 或更高版本

### 可选环境（生产部署）
- **Nginx**: 反向代理和负载均衡
- **PM2**: Node.js 进程管理
- **Docker**: 容器化部署

## 🚀 快速部署

### 1. 环境准备

```bash
# 检查 Java 版本
java -version

# 检查 Node.js 版本
node --version

# 检查 Maven 版本
mvn --version

# 检查 MySQL 服务
mysql --version
```

### 2. 数据库初始化

```bash
# 登录 MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE vtj_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构
USE vtj_db;
SOURCE src/main/resources/schema.sql;
```

### 3. 配置修改

修改 `src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    url: **********************************
    username: your_username
    password: your_password

vtj:
  coder:
    service:
      url: http://localhost:3001
```

### 4. 启动服务

```bash
# 使用启动脚本（推荐）
./start-services.sh

# 或手动启动
# 终端1：
cd vtj-coder-service && npm install && npm start

# 终端2：
mvn spring-boot:run
```

## 🐳 Docker 部署

### 1. 创建 Docker 网络

```bash
docker network create vtj-network
```

### 2. 启动 MySQL

```bash
docker run -d \
  --name vtj-mysql \
  --network vtj-network \
  -e MYSQL_ROOT_PASSWORD=root123 \
  -e MYSQL_DATABASE=vtj_db \
  -p 3306:3306 \
  mysql:8.0
```

### 3. 构建和启动服务

```bash
# 构建 Spring Boot 镜像
docker build -t vtj-backend .

# 构建 Node.js 微服务镜像
cd vtj-coder-service
docker build -t vtj-coder-service .

# 启动服务
docker run -d \
  --name vtj-coder \
  --network vtj-network \
  -p 3001:3001 \
  vtj-coder-service

docker run -d \
  --name vtj-backend \
  --network vtj-network \
  -p 8080:8080 \
  -e SPRING_DATASOURCE_URL=********************************** \
  vtj-backend
```

## 🔧 生产环境配置

### 1. Nginx 配置

创建 `/etc/nginx/sites-available/vtj-backend`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 代码转换服务代理
    location /coder/ {
        proxy_pass http://localhost:3001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 2. PM2 配置

创建 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: 'vtj-coder-service',
    script: './vtj-coder-service/index.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    }
  }]
};
```

启动 PM2：

```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 3. Spring Boot 生产配置

创建 `application-prod.yml`：

```yaml
server:
  port: 8080

spring:
  profiles:
    active: prod
  datasource:
    url: **********************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

logging:
  level:
    com.vtj.backend: INFO
  file:
    name: logs/vtj-backend.log

vtj:
  coder:
    service:
      url: http://localhost:3001
```

启动生产服务：

```bash
java -jar target/vtj-backend-1.0.0.jar --spring.profiles.active=prod
```

## 📊 监控和维护

### 1. 健康检查

```bash
# Spring Boot 健康检查
curl http://localhost:8080/api/actuator/health

# Node.js 服务健康检查
curl http://localhost:3001/health
```

### 2. 日志监控

```bash
# Spring Boot 日志
tail -f logs/vtj-backend.log

# PM2 日志
pm2 logs vtj-coder-service

# 系统资源监控
htop
```

### 3. 数据库维护

```sql
-- 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'vtj_db';

-- 清理过期数据（示例）
DELETE FROM schemas WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 🔒 安全配置

### 1. 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER 'vtj_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON vtj_db.* TO 'vtj_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 防火墙配置

```bash
# 只允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 8080   # 不直接暴露 Spring Boot 端口
ufw deny 3001   # 不直接暴露 Node.js 端口
ufw enable
```

### 3. SSL 证书

```bash
# 使用 Let's Encrypt
certbot --nginx -d your-domain.com
```

## 🚨 故障排查

### 常见问题

1. **端口冲突**
   ```bash
   # 查看端口占用
   netstat -tulpn | grep :8080
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **内存不足**
   ```bash
   # 调整 JVM 内存
   java -Xmx2g -Xms1g -jar vtj-backend.jar
   ```

3. **数据库连接失败**
   ```bash
   # 检查 MySQL 状态
   systemctl status mysql
   # 重启 MySQL
   systemctl restart mysql
   ```

## 📈 性能优化

### 1. JVM 调优

```bash
java -Xmx4g -Xms2g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -jar vtj-backend.jar
```

### 2. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_schemas_app_type ON schemas(app, type);
CREATE INDEX idx_schemas_created_at ON schemas(created_at);

-- 配置 MySQL
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
```

### 3. 缓存配置

在 Spring Boot 中启用缓存：

```java
@EnableCaching
@Configuration
public class CacheConfig {
    // 配置缓存策略
}
```
