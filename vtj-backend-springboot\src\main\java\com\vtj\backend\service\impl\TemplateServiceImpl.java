package com.vtj.backend.service.impl;

import com.alibaba.fastjson2.JSON;
import com.vtj.backend.entity.Template;
import com.vtj.backend.entity.TemplateDsl;
import com.vtj.backend.repository.TemplateRepository;
import com.vtj.backend.repository.TemplateDslRepository;
import com.vtj.backend.service.TemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 模板服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateServiceImpl implements TemplateService {

    private final TemplateRepository templateRepository;
    private final TemplateDslRepository templateDslRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTemplates(String platform, String category, String keyword) {
        List<Template> templates = templateRepository.searchTemplates(platform, category, keyword);
        return templates.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTemplate(String templateId) {
        Optional<Template> templateOpt = templateRepository.findByTemplateId(templateId);
        if (templateOpt.isPresent()) {
            Template template = templateOpt.get();
            // 增加浏览次数
            template.setViews(template.getViews() + 1);
            templateRepository.save(template);
            
            return entityToMap(template);
        }
        return new HashMap<>();
    }

    @Override
    @Transactional
    public String publishTemplate(String name, String description, String platform, 
                                 String category, String dsl, String coverUrl) {
        // 创建模板
        Template template = new Template();
        template.setTemplateId("template-" + System.currentTimeMillis());
        template.setName(name);
        template.setDescription(description);
        template.setPlatform(platform != null ? platform : "web");
        template.setCategory(category);
        template.setCover(coverUrl);
        template.setStatus(1);
        template.setViews(0);
        template.setDownloads(0);
        
        template = templateRepository.save(template);
        log.info("创建模板: {}", template.getTemplateId());

        // 保存DSL
        if (dsl != null && !dsl.trim().isEmpty()) {
            saveTemplateDsl(template.getTemplateId(), "1.0.0", dsl, "初始版本");
        }

        return template.getTemplateId();
    }

    @Override
    @Transactional
    public void removeTemplate(String templateId) {
        Optional<Template> templateOpt = templateRepository.findByTemplateId(templateId);
        if (templateOpt.isPresent()) {
            // 删除模板DSL
            templateDslRepository.deleteByTemplateId(templateId);
            // 删除模板
            templateRepository.delete(templateOpt.get());
            log.info("删除模板: {}", templateId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getTemplateDsl(String templateId) {
        Optional<TemplateDsl> dslOpt = templateDslRepository.findByTemplateIdAndIsCurrent(templateId, true);
        if (dslOpt.isPresent()) {
            TemplateDsl templateDsl = dslOpt.get();
            Map<String, Object> result = new HashMap<>();
            result.put("templateId", templateDsl.getTemplateId());
            result.put("version", templateDsl.getVersion());
            result.put("description", templateDsl.getDescription());
            result.put("updatedAt", templateDsl.getUpdatedAt());
            
            // 解析DSL内容
            try {
                result.put("dsl", JSON.parseObject(templateDsl.getDsl()));
            } catch (Exception e) {
                log.warn("解析模板DSL失败: {}", e.getMessage());
                result.put("dsl", new HashMap<>());
            }
            
            return result;
        }
        return new HashMap<>();
    }

    @Override
    @Transactional
    public void saveTemplateDsl(String templateId, String version, String dsl, String description) {
        // 将之前的版本设为非当前版本
        List<TemplateDsl> existingDsls = templateDslRepository.findByTemplateIdOrderByCreatedAtDesc(templateId);
        existingDsls.forEach(d -> {
            d.setIsCurrent(false);
            templateDslRepository.save(d);
        });

        // 创建新版本
        TemplateDsl templateDsl = new TemplateDsl();
        templateDsl.setTemplateId(templateId);
        templateDsl.setVersion(version);
        templateDsl.setDsl(dsl);
        templateDsl.setDescription(description);
        templateDsl.setIsCurrent(true);
        
        templateDslRepository.save(templateDsl);
        log.info("保存模板DSL: {} - {}", templateId, version);
    }

    @Override
    @Transactional
    public void incrementDownloads(String templateId) {
        Optional<Template> templateOpt = templateRepository.findByTemplateId(templateId);
        if (templateOpt.isPresent()) {
            Template template = templateOpt.get();
            template.setDownloads(template.getDownloads() + 1);
            templateRepository.save(template);
        }
    }

    /**
     * 实体转Map
     */
    private Map<String, Object> entityToMap(Template template) {
        Map<String, Object> map = new HashMap<>();
        map.put("templateId", template.getTemplateId());
        map.put("name", template.getName());
        map.put("description", template.getDescription());
        map.put("cover", template.getCover());
        map.put("platform", template.getPlatform());
        map.put("category", template.getCategory());
        map.put("tags", template.getTags());
        map.put("status", template.getStatus());
        map.put("views", template.getViews());
        map.put("downloads", template.getDownloads());
        map.put("createdAt", template.getCreatedAt());
        map.put("updatedAt", template.getUpdatedAt());
        return map;
    }
}
