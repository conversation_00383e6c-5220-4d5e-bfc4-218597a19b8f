# 前后端对接配置指南

## 1. 修改前端配置

在前端项目根目录的 `env.json` 文件中，修改后端地址：

```json
{
  "AUTH_CODE": "your-auth-code-here",
  "REMOTE": "http://localhost:8080/api"
}
```

## 2. 启动后端服务

确保两个后端服务都已启动：

```bash
# 方式一：使用启动脚本
./start-services.sh

# 方式二：手动启动
# 终端1：启动代码转换服务
cd vtj-coder-service && npm start

# 终端2：启动 Spring Boot 服务
mvn spring-boot:run
```

## 3. 验证服务状态

### 检查代码转换服务
```bash
curl http://localhost:3001/health
```

预期响应：
```json
{
  "status": "ok",
  "service": "vtj-coder-service",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 检查 Spring Boot 服务
```bash
curl http://localhost:8080/api/open/auth/test
```

预期响应：
```json
{
  "success": true,
  "token": "mock-token-123",
  "user": {
    "id": 1,
    "username": "demo_user",
    "email": "<EMAIL>",
    "avatar": "",
    "status": 1
  }
}
```

## 4. 启动前端项目

```bash
# 在前端项目目录
npm run dev
```

## 5. 功能测试清单

### ✅ 基础功能测试

- [ ] **用户认证**：能否正常登录/获取用户信息
- [ ] **应用管理**：能否创建、查看应用列表
- [ ] **设计器加载**：能否正常打开设计器界面
- [ ] **组件拖拽**：能否正常拖拽组件到画布
- [ ] **属性编辑**：能否正常编辑组件属性
- [ ] **页面保存**：能否正常保存页面设计
- [ ] **页面预览**：能否正常预览页面效果

### ✅ 代码转换测试

- [ ] **DSL 转 Vue**：设计器中的预览是否正常显示
- [ ] **Vue 转 DSL**：导入 Vue 代码是否能正确解析
- [ ] **代码生成**：能否生成正确的 Vue 代码

### ✅ 扩展功能测试

- [ ] **模板功能**：能否正常浏览和使用模板
- [ ] **项目出码**：能否导出完整项目代码
- [ ] **历史记录**：能否正常查看和恢复历史版本

## 6. 常见问题排查

### 问题1：前端无法连接后端
**症状**：前端显示网络错误或接口调用失败
**解决方案**：
1. 检查后端服务是否正常启动
2. 确认端口号是否正确（Spring Boot: 8080, Node.js: 3001）
3. 检查防火墙设置
4. 查看浏览器控制台错误信息

### 问题2：代码转换功能不工作
**症状**：DSL 转 Vue 或 Vue 转 DSL 失败
**解决方案**：
1. 检查 Node.js 微服务是否正常运行
2. 查看 Spring Boot 日志中的错误信息
3. 确认 @vtj/coder 和 @vtj/parser 包是否正确安装

### 问题3：数据库连接失败
**症状**：Schema 保存/查询失败
**解决方案**：
1. 确认 MySQL 服务是否启动
2. 检查数据库连接配置（application.yml）
3. 确认数据库 vtj_db 是否已创建
4. 运行 schema.sql 初始化表结构

### 问题4：跨域问题
**症状**：浏览器控制台显示 CORS 错误
**解决方案**：
1. 检查 Spring Boot 的 CORS 配置
2. 确认前端请求地址是否正确
3. 如果是生产环境，配置正确的域名白名单

## 7. 性能优化建议

1. **开发环境**：
   - 使用 `npm run dev` 启动 Node.js 服务（支持热重载）
   - 启用 Spring Boot 的开发者工具

2. **生产环境**：
   - 使用 PM2 管理 Node.js 进程
   - 配置 Nginx 反向代理
   - 启用 Gzip 压缩
   - 配置数据库连接池

## 8. 监控和日志

### 查看服务日志
```bash
# Spring Boot 日志
tail -f logs/spring.log

# Node.js 服务日志
# 如果使用 PM2
pm2 logs vtj-coder-service
```

### 监控服务状态
```bash
# 检查端口占用
netstat -tulpn | grep :8080
netstat -tulpn | grep :3001

# 检查进程状态
ps aux | grep java
ps aux | grep node
```
