import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简化的Vue转DSL函数
function simpleVueToDsl(vueContent) {
  try {
    // 提取template部分
    const templateMatch = vueContent.match(/<template>([\s\S]*?)<\/template>/);
    const scriptMatch = vueContent.match(/<script>([\s\S]*?)<\/script>/);
    const styleMatch = vueContent.match(/<style[^>]*>([\s\S]*?)<\/style>/);
    
    if (!templateMatch) {
      throw new Error('未找到template部分');
    }
    
    const template = templateMatch[1].trim();
    const script = scriptMatch ? scriptMatch[1].trim() : '';
    const style = styleMatch ? styleMatch[1].trim() : '';
    
    // 简单的DSL结构
    const dsl = {
      type: 'page',
      name: '123',
      meta: {
        title: '用户管理系统',
        description: '本地同步测试页面'
      },
      template: template,
      script: script,
      style: style,
      components: [],
      data: {},
      methods: {},
      computed: {},
      lifecycle: {}
    };
    
    return dsl;
    
  } catch (error) {
    console.error('Vue转DSL失败:', error.message);
    return null;
  }
}

async function testSimpleVueToDsl() {
  try {
    // 读取Vue文件内容
    const vueFilePath = path.join(__dirname, 'vtj-sync-tool/src/pages/123.vue');
    const vueContent = fs.readFileSync(vueFilePath, 'utf-8');
    
    console.log('📄 Vue文件内容长度:', vueContent.length);
    
    // 转换为DSL
    const dsl = simpleVueToDsl(vueContent);
    
    if (dsl) {
      console.log('✅ 转换成功!');
      console.log('📋 DSL结构:', {
        type: dsl.type,
        name: dsl.name,
        meta: dsl.meta,
        templateLength: dsl.template.length,
        scriptLength: dsl.script.length,
        styleLength: dsl.style.length
      });
      
      // 保存DSL到文件
      fs.writeFileSync('test-dsl-output.json', JSON.stringify(dsl, null, 2));
      console.log('💾 DSL已保存到 test-dsl-output.json');
      
    } else {
      console.log('❌ 转换失败');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testSimpleVueToDsl();
