package com.vtj.backend.service.impl;

import com.vtj.backend.dto.AppDto;
import com.vtj.backend.dto.SchemaDto;
import com.vtj.backend.entity.App;
import com.vtj.backend.exception.BusinessException;
import com.vtj.backend.repository.AppRepository;
import com.vtj.backend.service.AppService;
import com.vtj.backend.service.SchemaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 应用服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppServiceImpl implements AppService {

    private final AppRepository appRepository;
    private final SchemaService schemaService;

    @Override
    @Transactional
    public Map<String, Object> createApp(AppDto dto) {
        // 处理应用ID：优先使用 name 字段，如果没有则使用 appId
        String appId = dto.getName() != null ? dto.getName().trim() : 
                      (dto.getAppId() != null ? dto.getAppId().trim() : null);
        String appName = dto.getLabel() != null ? dto.getLabel().trim() : 
                        (dto.getName() != null ? dto.getName().trim() : 
                         (dto.getAppId() != null ? dto.getAppId().trim() : null));
        
        if (appId == null || appId.isEmpty()) {
            throw new BusinessException(400, "应用标识不能为空");
        }

        // 验证应用ID格式（可选）
        if (!isValidAppId(appId)) {
            throw new BusinessException(400, "应用ID格式无效，只能包含字母、数字、下划线和点号");
        }

        // 检查应用ID是否已存在
        Optional<App> existingApp = appRepository.findByAppId(appId);
        if (existingApp.isPresent()) {
            log.warn("尝试创建重复的应用ID: {}", appId);
            throw new BusinessException(409, "应用ID已存在: " + appId + "，请使用其他标识");
        }

        try {
            App app = new App();
            app.setAppId(appId);
            app.setName(appName);
            app.setDescription(dto.getDescription());
            app.setIcon(dto.getIcon());
            app.setPlatform(dto.getPlatform() != null ? dto.getPlatform() : "Web");
            app.setScope(dto.getScope() != null ? dto.getScope() : "protected");
            app.setStatus(dto.getStatus() != null ? dto.getStatus() : 1);
            app.setUserId(dto.getUserId() != null ? dto.getUserId() : 1L); // 默认用户ID

            app = appRepository.save(app);
            log.info("创建应用成功: {} - {} (平台: {}, 权限: {})", appId, appName, app.getPlatform(), app.getScope());

            // 异步初始化默认Schema，避免阻塞应用创建
            try {
                initializeDefaultSchemas(appId);
                log.info("应用默认Schema初始化完成: {}", appId);
            } catch (Exception e) {
                log.warn("初始化默认Schema失败，但不影响应用创建: {}", appId, e);
            }

            return entityToMap(app);
        } catch (Exception e) {
            log.error("创建应用失败: {}", appId, e);
            throw new BusinessException(500, "创建应用失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public Map<String, Object> updateApp(String appId, AppDto dto) {
        if (appId == null || appId.trim().isEmpty()) {
            throw new BusinessException(400, "应用ID不能为空");
        }

        try {
            // 查找现有应用
            Optional<App> existingAppOpt = appRepository.findByAppId(appId.trim());
            if (existingAppOpt.isEmpty()) {
                throw new BusinessException(404, "应用不存在: " + appId);
            }

            App app = existingAppOpt.get();

            // 更新应用信息（不允许修改appId）
            if (dto.getLabel() != null) {
                app.setName(dto.getLabel().trim());
            }
            if (dto.getDescription() != null) {
                app.setDescription(dto.getDescription());
            }
            if (dto.getIcon() != null) {
                app.setIcon(dto.getIcon());
            }
            if (dto.getPlatform() != null) {
                app.setPlatform(dto.getPlatform());
            }
            if (dto.getScope() != null) {
                app.setScope(dto.getScope());
            }
            if (dto.getStatus() != null) {
                app.setStatus(dto.getStatus());
            }

            app = appRepository.save(app);
            log.info("更新应用成功: {} - {} (平台: {}, 权限: {})",
                    appId, app.getName(), app.getPlatform(), app.getScope());

            return entityToMap(app);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新应用失败: {}", appId, e);
            throw new BusinessException(500, "更新应用失败，请稍后重试");
        }
    }

    /**
     * 验证应用ID格式
     */
    private boolean isValidAppId(String appId) {
        // 应用ID只能包含字母、数字、下划线、点号和短横线
        return appId.matches("^[a-zA-Z0-9._-]+$") && appId.length() <= 50;
    }

    /**
     * 初始化应用的默认Schema
     */
    private void initializeDefaultSchemas(String appId) {
        try {
            // 创建默认项目Schema
            SchemaDto projectSchema = new SchemaDto();
            projectSchema.setApp(appId);
            projectSchema.setType("project");
            projectSchema.setName(appId);
            projectSchema.setContent(createDefaultProjectContent(appId));
            projectSchema.setDescription("默认项目配置");
            schemaService.save(projectSchema);

            // 创建默认物料Schema
            SchemaDto materialSchema = new SchemaDto();
            materialSchema.setApp(appId);
            materialSchema.setType("material");
            materialSchema.setName(appId);
            materialSchema.setContent(createDefaultMaterialContent());
            materialSchema.setDescription("默认物料配置");
            schemaService.save(materialSchema);

            log.info("默认Schema创建成功: {}", appId);
        } catch (Exception e) {
            log.error("创建默认Schema失败: {}", appId, e);
            throw e;
        }
    }

    /**
     * 创建默认项目配置内容
     */
    private String createDefaultProjectContent(String appId) {
        return String.format("""
            {
              "id": "%s",
              "name": "%s",
              "title": "%s项目",
              "description": "VTJ低代码平台生成的项目",
              "platform": "web",
              "version": "1.0.0",
              "pages": [],
              "blocks": [],
              "dependencies": [],
              "config": {
                "title": "%s项目"
              }
            }
            """, appId, appId, appId, appId);
    }

    /**
     * 创建默认物料配置内容
     */
    private String createDefaultMaterialContent() {
        return """
            {
              "components": {
                "div": {
                  "name": "div",
                  "title": "容器",
                  "category": "基础",
                  "props": {}
                },
                "text": {
                  "name": "text",
                  "title": "文本",
                  "category": "基础",
                  "props": {
                    "content": "文本内容"
                  }
                }
              },
              "blocks": {},
              "templates": {}
            }
            """;
    }

    @Override
    public List<Map<String, Object>> findMyApps(Long userId) {
        try {
            if (userId == null) {
                // 如果没有用户ID，返回所有应用（临时处理）
                return findAllActiveApps();
            }
            
            List<App> apps = appRepository.findByUserIdOrderByCreatedAtDesc(userId);
            return apps.stream()
                    .map(this::entityToMap)
                    .toList();
        } catch (Exception e) {
            log.error("查询用户应用失败: userId={}", userId, e);
            throw new BusinessException(500, "查询应用列表失败");
        }
    }

    @Override
    public Map<String, Object> findByAppId(String appId) {
        try {
            if (appId == null || appId.trim().isEmpty()) {
                throw new BusinessException(400, "应用ID不能为空");
            }
            
            Optional<App> appOpt = appRepository.findByAppId(appId.trim());
            return appOpt.map(this::entityToMap).orElse(new HashMap<>());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询应用失败: appId={}", appId, e);
            throw new BusinessException(500, "查询应用失败");
        }
    }

    @Override
    public List<Map<String, Object>> findAllActiveApps() {
        try {
            List<App> apps = appRepository.findByStatusOrderByCreatedAtDesc(1);
            return apps.stream()
                    .map(this::entityToMap)
                    .toList();
        } catch (Exception e) {
            log.error("查询活跃应用失败", e);
            throw new BusinessException(500, "查询应用列表失败");
        }
    }

    @Override
    @Transactional
    public boolean deleteApp(String id) {
        try {
            log.info("开始删除应用: id={}", id);

            // 将字符串ID转换为Long类型
            Long appId;
            try {
                appId = Long.parseLong(id);
            } catch (NumberFormatException e) {
                log.error("无效的应用ID格式: {}", id);
                throw new BusinessException(400, "无效的应用ID格式");
            }

            // 检查应用是否存在
            Optional<App> appOpt = appRepository.findById(appId);
            if (appOpt.isEmpty()) {
                log.warn("应用不存在: id={}", id);
                return false;
            }

            App app = appOpt.get();
            log.info("找到应用: {} - {}", app.getAppId(), app.getName());

            // 删除应用记录
            appRepository.deleteById(appId);
            log.info("应用删除成功: id={}", id);

            return true;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除应用失败: id={}", id, e);
            throw new BusinessException(500, "删除应用失败，请稍后重试");
        }
    }

    /**
     * 实体转换为 Map
     */
    private Map<String, Object> entityToMap(App app) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", app.getId());
        map.put("appId", app.getAppId());
        // 前端期望的字段名称
        map.put("name", app.getAppId()); // 前端使用name作为应用标识
        map.put("label", app.getName()); // 前端使用label作为显示名称
        map.put("platform", app.getPlatform()); // 使用实际的平台类型
        map.put("scope", app.getScope()); // 使用实际的权限范围
        map.put("description", app.getDescription());
        map.put("icon", app.getIcon());
        map.put("status", app.getStatus());
        map.put("userId", app.getUserId());
        map.put("createdAt", app.getCreatedAt());
        map.put("updatedAt", app.getUpdatedAt());
        return map;
    }
} 