package com.vtj.backend.repository;

import com.vtj.backend.entity.Template;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 模板数据访问层
 */
@Repository
public interface TemplateRepository extends JpaRepository<Template, Long> {

    /**
     * 根据模板ID查找
     */
    Optional<Template> findByTemplateId(String templateId);

    /**
     * 根据平台查找模板列表
     */
    List<Template> findByPlatformAndStatus(String platform, Integer status);

    /**
     * 根据平台查找所有模板
     */
    List<Template> findByPlatform(String platform);

    /**
     * 查找所有发布状态的模板
     */
    List<Template> findByStatus(Integer status);

    /**
     * 根据用户ID查找模板
     */
    List<Template> findByUserId(Long userId);

    /**
     * 根据分类查找模板
     */
    List<Template> findByCategory(String category);

    /**
     * 搜索模板
     */
    @Query("SELECT t FROM Template t WHERE " +
           "(:platform IS NULL OR t.platform = :platform) AND " +
           "(:category IS NULL OR t.category = :category) AND " +
           "(:keyword IS NULL OR t.name LIKE %:keyword% OR t.description LIKE %:keyword%) AND " +
           "t.status = 1")
    List<Template> searchTemplates(@Param("platform") String platform,
                                  @Param("category") String category,
                                  @Param("keyword") String keyword);
}
