import { createApi, toArray } from '@vtj/utils';
import { SchemaType, REMOTE } from '@/shared';

export interface GetSchemaReq {
  type: SchemaType;
  app: string;
  name: string;
}

export interface SaveSchemaReq extends GetSchemaReq {
  content: Record<string, any>;
}

export interface RemoveSchemaReq extends Omit<GetSchemaReq, 'name'> {
  name: string | string[];
}

export interface FileGeneratorReq {
  app: string;
  platform: string;
  dsl: Record<string, any>;
}

export interface ProjectGeneratorReq {
  app: string;
  project: Record<string, any>;
}

export interface VueParserReq {
  // 文件id
  id: string;
  // 组件名称
  name: string;
  // Vue源码
  source: string;
  // 项目 dsl
  project: Record<string, any>;
}

export interface SchemaRes {
  id: string;
  app: string;
  name: string;
  type: SchemaType;
  content: Record<string, any>;
}

/**
 * 获取 Schema
 * @param req
 * @returns
 */
export const getSchema = (req: GetSchemaReq) => {
  console.log('🔧 getSchema API 调用开始:', {
    app: req.app,
    type: req.type,
    name: req.name,
    baseURL: REMOTE
  });

  const api = createApi<SchemaRes>({
    baseURL: REMOTE,
    url: '/schemas/info/:app/:type',
    method: 'get'
  });
  const { type, app, name } = req;

  return api({ name }, { params: { app, type } })
    .then((response) => {
      console.log('✅ getSchema API 成功响应:', response);
      return response;
    })
    .catch((error) => {
      console.warn('⚠️ getSchema API 调用失败，返回默认结构:', error);

      // 返回默认的空Schema结构，而不是抛出错误
      const defaultSchema: SchemaRes = {
        id: '',
        app: req.app,
        name: req.name,
        type: req.type,
        content: {}
      };

      console.log('📝 返回默认Schema结构:', defaultSchema);
      return defaultSchema;
    });
};

/**
 * 获取 Schema 列表
 * @param req
 * @returns
 */
export const getSchemaList = (req: { app: string; type: string; name?: string }) => {
  const api = createApi<SchemaRes[]>({
    baseURL: REMOTE,
    url: '/schemas/:app/:type',
    method: 'get'
  });
  const { type, app, name } = req;
  const query = name ? { name } : {};
  return api(query, { params: { app, type } });
};

/**
 * 新建、更新Schema
 * @param req
 * @returns
 */
export const saveSchema = (req: SaveSchemaReq) => {
  console.log('🔧 saveSchema API 调用开始:', {
    app: req.app,
    type: req.type,
    name: req.name,
    contentType: typeof req.content,
    contentKeys: Object.keys(req.content || {})
  });

  const api = createApi<SchemaRes>({
    baseURL: REMOTE,
    url: '/schemas/:app/:type',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { type, app, name, content } = req;

  const requestData = {
    app,
    type,
    name,
    content: JSON.stringify(content)
  };

  console.log('🔧 saveSchema 发送请求数据:', requestData);

  return api(requestData, { params: { app, type } })
    .then((response) => {
      console.log('✅ saveSchema API 成功响应:', response);
      return response;
    })
    .catch((error) => {
      console.error('❌ saveSchema API 错误:', error);

      // 提取更详细的错误信息
      let errorMessage = 'Schema保存失败';
      if (error && typeof error === 'object') {
        if (error.message) {
          errorMessage = error.message;
        } else if (error.data && error.data.message) {
          errorMessage = error.data.message;
        } else if (error.response) {
          if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.status) {
            errorMessage = `HTTP ${error.response.status}: ${error.response.statusText || '请求失败'}`;
          }
        }
      }

      console.error('❌ saveSchema 详细错误信息:', errorMessage);

      // 创建一个包含详细信息的错误对象
      const detailedError = new Error(errorMessage) as Error & { originalError?: any };
      detailedError.originalError = error;
      throw detailedError;
    });
};

/**
 * 删除Schema
 * @param req
 * @returns
 */
export const removeSchema = (req: RemoveSchemaReq) => {
  const api = createApi<boolean>({
    baseURL: REMOTE,
    url: '/schemas/:app/:type',
    method: 'delete',
    settings: {
      type: 'json'
    }
  });
  const { type, app, name } = req;
  return api(toArray(name), { params: { app, type } });
};

/**
 * DSL出码，DSL -> Vue
 * @param req
 * @returns
 */
export const fileGenerator = (req: FileGeneratorReq) => {
  const api = createApi<string>({
    baseURL: REMOTE,
    url: '/schemas/generator/:app/vue',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { app, platform, dsl } = req;
  return api(dsl, { params: { app }, query: { platform } });
};

/**
 * 项目出码
 * @param req
 * @returns
 */
export const projectGenerator = (req: ProjectGeneratorReq) => {
  const api = createApi<string>({
    baseURL: REMOTE,
    url: '/schemas/generator/:app/project',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { app, project } = req;
  return api(project, { params: { app } });
};

/**
 * 解析Vue代码， Vue -> DSL
 * @param req
 * @returns
 */
export const vueParser = (req: VueParserReq) => {
  const api = createApi<Record<string, any>>({
    baseURL: REMOTE,
    url: '/schemas/parser',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  return api({
    ...req,
    project: JSON.stringify(req.project)
  });
};
