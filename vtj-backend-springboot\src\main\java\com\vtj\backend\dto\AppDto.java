package com.vtj.backend.dto;

import lombok.Data;

/**
 * 应用数据传输对象
 */
@Data
public class AppDto {

    /**
     * 应用ID (前端可能叫 name)
     */
    private String appId;
    
    /**
     * 前端可能使用 name 字段作为应用标识
     */
    private String name;

    /**
     * 应用标题 (前端可能叫 label)
     */
    private String label;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标
     */
    private String icon;

    /**
     * 应用权限范围 (前端字段)
     */
    private String scope;

    /**
     * 平台类型 (前端字段)
     */
    private String platform;

    /**
     * 状态
     */
    private Integer status = 1;

    /**
     * 创建用户ID
     */
    private Long userId;
} 