package com.vtj.backend.repository;

import com.vtj.backend.entity.TemplateDsl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 模板DSL数据访问层
 */
@Repository
public interface TemplateDslRepository extends JpaRepository<TemplateDsl, Long> {

    /**
     * 根据模板ID查找所有版本
     */
    List<TemplateDsl> findByTemplateIdOrderByCreatedAtDesc(String templateId);

    /**
     * 根据模板ID查找当前版本
     */
    Optional<TemplateDsl> findByTemplateIdAndIsCurrent(String templateId, Boolean isCurrent);

    /**
     * 根据模板ID和版本号查找
     */
    Optional<TemplateDsl> findByTemplateIdAndVersion(String templateId, String version);

    /**
     * 根据模板ID删除所有版本
     */
    void deleteByTemplateId(String templateId);
}
