package com.vtj.backend.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Schema 数据传输对象
 */
@Data
public class SchemaDto {

    @NotBlank(message = "应用ID不能为空")
    private String app;

    @NotBlank(message = "类型不能为空")
    private String type;

    @NotBlank(message = "名称不能为空")
    private String name;

    /**
     * DSL内容(JSON字符串)
     */
    private String content;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 描述
     */
    private String description;

    /**
     * 用户ID
     */
    private Long userId;
} 