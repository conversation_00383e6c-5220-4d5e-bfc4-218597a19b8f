{"name": "vtj-sync-tool", "version": "1.0.0", "description": "VTJ低代码平台本地同步工具 - 实现本地Vue文件与平台DSL的双向同步", "main": "vtj-sync.js", "bin": {"vtj-sync": "./cli.js"}, "scripts": {"start": "node vtj-sync.js", "watch": "node vtj-sync.js watch", "pull": "node vtj-sync.js pull", "push": "node vtj-sync.js push", "init": "node vtj-sync.js init"}, "keywords": ["vtj", "lowcode", "sync", "vue", "dsl"], "author": "VTJ Sync Tool", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chokidar": "^3.5.3", "commander": "^11.1.0", "chalk": "^4.1.2", "ora": "^5.4.1", "inquirer": "^8.2.6", "fs-extra": "^11.1.1", "path": "^0.12.7", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}