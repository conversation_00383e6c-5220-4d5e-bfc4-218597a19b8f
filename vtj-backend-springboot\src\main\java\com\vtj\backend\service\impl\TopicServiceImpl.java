package com.vtj.backend.service.impl;

import com.vtj.backend.dto.UserTopicDto;
import com.vtj.backend.entity.Topic;
import com.vtj.backend.repository.TopicRepository;
import com.vtj.backend.repository.ChatRepository;
import com.vtj.backend.service.TopicService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * AI话题服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TopicServiceImpl implements TopicService {

    private final TopicRepository topicRepository;
    private final ChatRepository chatRepository;

    @Override
    @Transactional
    public String createTopic(UserTopicDto dto) {
        Topic topic = new Topic();
        topic.setTopicId("topic-" + System.currentTimeMillis());
        topic.setTitle(dto.getTitle());
        topic.setType(dto.getType() != null ? dto.getType() : "text");
        topic.setModel(dto.getModel() != null ? dto.getModel() : "gpt-3.5-turbo");
        topic.setFileUrl(dto.getFileUrl());
        topic.setPrompt(dto.getPrompt());
        topic.setStatus(1);
        // TODO: 从认证信息中获取用户ID
        topic.setUserId(1L);

        topic = topicRepository.save(topic);
        log.info("创建话题: {}", topic.getTopicId());

        return topic.getTopicId();
    }

    @Override
    @Transactional
    public String createImageTopic(String title, String prompt, String fileUrl) {
        Topic topic = new Topic();
        topic.setTopicId("topic-img-" + System.currentTimeMillis());
        topic.setTitle(title != null ? title : "图片分析");
        topic.setType("image");
        topic.setModel("gpt-4-vision");
        topic.setFileUrl(fileUrl);
        topic.setPrompt(prompt);
        topic.setStatus(1);
        topic.setUserId(1L);

        topic = topicRepository.save(topic);
        log.info("创建图片话题: {}", topic.getTopicId());

        return topic.getTopicId();
    }

    @Override
    @Transactional
    public String createJsonTopic(String title, String prompt, String fileUrl) {
        Topic topic = new Topic();
        topic.setTopicId("topic-json-" + System.currentTimeMillis());
        topic.setTitle(title != null ? title : "JSON分析");
        topic.setType("json");
        topic.setModel("gpt-3.5-turbo");
        topic.setFileUrl(fileUrl);
        topic.setPrompt(prompt);
        topic.setStatus(1);
        topic.setUserId(1L);

        topic = topicRepository.save(topic);
        log.info("创建JSON话题: {}", topic.getTopicId());

        return topic.getTopicId();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopics(Long userId, String fileId) {
        List<Topic> topics;
        
        if (fileId != null && !fileId.trim().isEmpty()) {
            topics = topicRepository.findByFileId(fileId);
        } else if (userId != null) {
            topics = topicRepository.findByUserIdOrderByCreatedAtDesc(userId);
        } else {
            topics = topicRepository.findHotTopics();
        }

        return topics.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getHotTopics() {
        List<Topic> topics = topicRepository.findHotTopics();
        return topics.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional
    public void removeTopic(String topicId) {
        Optional<Topic> topicOpt = topicRepository.findByTopicId(topicId);
        if (topicOpt.isPresent()) {
            // 删除相关对话
            chatRepository.deleteByTopicId(topicId);
            // 删除话题
            topicRepository.delete(topicOpt.get());
            log.info("删除话题: {}", topicId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopicsByFileId(String fileId) {
        List<Topic> topics = topicRepository.findByFileId(fileId);
        return topics.stream()
                .map(this::entityToMap)
                .toList();
    }

    /**
     * 实体转Map
     */
    private Map<String, Object> entityToMap(Topic topic) {
        Map<String, Object> map = new HashMap<>();
        map.put("topicId", topic.getTopicId());
        map.put("title", topic.getTitle());
        map.put("type", topic.getType());
        map.put("model", topic.getModel());
        map.put("fileUrl", topic.getFileUrl());
        map.put("prompt", topic.getPrompt());
        map.put("status", topic.getStatus());
        map.put("userId", topic.getUserId());
        map.put("createdAt", topic.getCreatedAt());
        map.put("updatedAt", topic.getUpdatedAt());
        return map;
    }
}
