import type { App } from 'vue';
import {
  widgetManager,
  depsManager,
  type ProjectModel,
  type BlockModel,
  type Engine
} from '@vtj/pro';
import { createPreviewPath, createCurrrentPagePath } from '@/utils';
import { BASE_PATH } from '@/shared';

/**
 * 设置动作按钮
 */
widgetManager.set('Actions', {
  props: {
    // 显示发布模版按钮
    onlyPublishTemplate: true,
    // 显示出码按钮
    coder: true
  }
});

/**
 *  设置左上角的切换按钮点击事件动作
 */
widgetManager.set('Switcher', {
  props: {
    onClick: (project: ProjectModel) => {
      const path = createCurrrentPagePath(project);
      window.open(path, 'VtjPage');
    }
  }
});

/**
 * 设置预览视图加载的页面路径
 */
widgetManager.set('Previewer', {
  props: {
    path: (block: BlockModel, project: ProjectModel) => {
      const { id, platform } = project;
      return createPreviewPath(id, platform, block.id);
    }
  }
});

/**
 * 点击Logo返回首页
 */
widgetManager.set('Logo', {
  props: {
    link: BASE_PATH
  }
});

export function install(_app: App, _engine?: Engine) {
  // console.log(_app, _engine);

  // 注册Element Plus依赖
  depsManager.add({
    version: 'latest',
    css: 'https://unpkg.com/element-plus/dist/index.css',
    js: 'https://unpkg.com/element-plus/dist/index.full.js',
    library: 'ElementPlus'
  } as any);

  // 注册Vue依赖
  depsManager.add({
    version: 'latest',
    js: 'https://unpkg.com/vue@next/dist/vue.global.js',
    library: 'Vue'
  } as any);

  // 注册Pinia依赖
  depsManager.add({
    version: '2.1.7',
    js: '/@vtj/materials/deps/pinia/pinia.iife.js',
    library: 'Pinia'
  } as any);
}
