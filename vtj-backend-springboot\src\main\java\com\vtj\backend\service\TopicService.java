package com.vtj.backend.service;

import com.vtj.backend.dto.UserTopicDto;

import java.util.List;
import java.util.Map;

/**
 * AI话题服务接口
 */
public interface TopicService {

    /**
     * 创建话题
     */
    String createTopic(UserTopicDto dto);

    /**
     * 创建图片话题
     */
    String createImageTopic(String title, String prompt, String fileUrl);

    /**
     * 创建JSON话题
     */
    String createJsonTopic(String title, String prompt, String fileUrl);

    /**
     * 获取话题列表
     */
    List<Map<String, Object>> getTopics(Long userId, String fileId);

    /**
     * 获取热门话题
     */
    List<Map<String, Object>> getHotTopics();

    /**
     * 删除话题
     */
    void removeTopic(String topicId);

    /**
     * 根据文件ID查找话题
     */
    List<Map<String, Object>> getTopicsByFileId(String fileId);
}
