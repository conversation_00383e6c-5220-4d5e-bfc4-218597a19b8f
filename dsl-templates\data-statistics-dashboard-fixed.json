{"id": "data-statistics-page", "name": "数据统计管理页面", "platform": "web", "version": "1.0.0", "description": "包含侧边栏、统计卡片、图表和表单的数据管理页面", "children": [{"id": "layout-container", "name": "ElC<PERSON><PERSON>", "props": {"direction": "horizontal", "style": {"height": "100vh", "background": "#f5f5f5"}}, "children": [{"id": "sidebar", "name": "ElAside", "props": {"width": "200px", "style": {"background": "#2c3e50", "color": "#fff", "padding": "20px 0"}}, "children": [{"id": "sidebar-title", "name": "div", "props": {"style": {"padding": "0 20px 20px", "fontSize": "16px", "fontWeight": "bold", "borderBottom": "1px solid #34495e", "marginBottom": "20px"}}, "children": [{"id": "title-text", "name": "span", "props": {"innerHTML": "数据统计管理系统"}}]}, {"id": "sidebar-menu", "name": "ElMenu", "props": {"defaultActive": "dashboard", "backgroundColor": "#2c3e50", "textColor": "#bdc3c7", "activeTextColor": "#3498db", "uniqueOpened": true}, "children": [{"id": "menu-dashboard", "name": "ElMenuItem", "props": {"index": "dashboard"}, "children": [{"id": "dashboard-text", "name": "span", "props": {"innerHTML": "📊 仪表盘"}}]}, {"id": "menu-users", "name": "ElMenuItem", "props": {"index": "users"}, "children": [{"id": "users-text", "name": "span", "props": {"innerHTML": "👥 用户管理"}}]}, {"id": "menu-orders", "name": "ElMenuItem", "props": {"index": "orders"}, "children": [{"id": "orders-text", "name": "span", "props": {"innerHTML": "📦 订单管理"}}]}, {"id": "menu-products", "name": "ElMenuItem", "props": {"index": "products"}, "children": [{"id": "products-text", "name": "span", "props": {"innerHTML": "🛍️ 商品管理"}}]}, {"id": "menu-finance", "name": "ElMenuItem", "props": {"index": "finance"}, "children": [{"id": "finance-text", "name": "span", "props": {"innerHTML": "💰 财务管理"}}]}, {"id": "menu-reports", "name": "ElMenuItem", "props": {"index": "reports"}, "children": [{"id": "reports-text", "name": "span", "props": {"innerHTML": "📈 报表分析"}}]}]}]}, {"id": "main-content", "name": "<PERSON><PERSON><PERSON>", "props": {"style": {"padding": "20px", "background": "#fff"}}, "children": [{"id": "page-header", "name": "div", "props": {"style": {"marginBottom": "20px", "paddingBottom": "15px", "borderBottom": "1px solid #e0e0e0"}}, "children": [{"id": "page-title", "name": "h2", "props": {"innerHTML": "数据统计概览", "style": {"margin": "0", "color": "#2c3e50", "fontSize": "24px"}}}]}, {"id": "stats-cards-row", "name": "ElRow", "props": {"gutter": 20, "style": {"marginBottom": "30px"}}, "children": [{"id": "stats-card-1", "name": "ElCol", "props": {"span": 6}, "children": [{"id": "card-1", "name": "ElCard", "props": {"shadow": "hover", "style": {"textAlign": "center", "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "color": "#fff", "border": "none", "borderRadius": "10px"}}, "children": [{"id": "card-1-content", "name": "div", "props": {"style": {"padding": "20px"}}, "children": [{"id": "card-1-icon", "name": "div", "props": {"innerHTML": "📊", "style": {"fontSize": "40px", "marginBottom": "10px"}}}, {"id": "card-1-title", "name": "div", "props": {"innerHTML": "今日访问", "style": {"fontSize": "14px", "marginBottom": "5px", "opacity": "0.9"}}}, {"id": "card-1-value", "name": "div", "props": {"innerHTML": "1,234", "style": {"fontSize": "32px", "fontWeight": "bold"}}}]}]}]}, {"id": "stats-card-2", "name": "ElCol", "props": {"span": 6}, "children": [{"id": "card-2", "name": "ElCard", "props": {"shadow": "hover", "style": {"textAlign": "center", "background": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "color": "#fff", "border": "none", "borderRadius": "10px"}}, "children": [{"id": "card-2-content", "name": "div", "props": {"style": {"padding": "20px"}}, "children": [{"id": "card-2-icon", "name": "div", "props": {"innerHTML": "💰", "style": {"fontSize": "40px", "marginBottom": "10px"}}}, {"id": "card-2-title", "name": "div", "props": {"innerHTML": "销售额", "style": {"fontSize": "14px", "marginBottom": "5px", "opacity": "0.9"}}}, {"id": "card-2-value", "name": "div", "props": {"innerHTML": "¥56,789", "style": {"fontSize": "32px", "fontWeight": "bold"}}}]}]}]}, {"id": "stats-card-3", "name": "ElCol", "props": {"span": 6}, "children": [{"id": "card-3", "name": "ElCard", "props": {"shadow": "hover", "style": {"textAlign": "center", "background": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "color": "#fff", "border": "none", "borderRadius": "10px"}}, "children": [{"id": "card-3-content", "name": "div", "props": {"style": {"padding": "20px"}}, "children": [{"id": "card-3-icon", "name": "div", "props": {"innerHTML": "👥", "style": {"fontSize": "40px", "marginBottom": "10px"}}}, {"id": "card-3-title", "name": "div", "props": {"innerHTML": "用户数", "style": {"fontSize": "14px", "marginBottom": "5px", "opacity": "0.9"}}}, {"id": "card-3-value", "name": "div", "props": {"innerHTML": "8,456", "style": {"fontSize": "32px", "fontWeight": "bold"}}}]}]}]}, {"id": "stats-card-4", "name": "ElCol", "props": {"span": 6}, "children": [{"id": "card-4", "name": "ElCard", "props": {"shadow": "hover", "style": {"textAlign": "center", "background": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)", "color": "#fff", "border": "none", "borderRadius": "10px"}}, "children": [{"id": "card-4-content", "name": "div", "props": {"style": {"padding": "20px"}}, "children": [{"id": "card-4-icon", "name": "div", "props": {"innerHTML": "📦", "style": {"fontSize": "40px", "marginBottom": "10px"}}}, {"id": "card-4-title", "name": "div", "props": {"innerHTML": "订单数", "style": {"fontSize": "14px", "marginBottom": "5px", "opacity": "0.9"}}}, {"id": "card-4-value", "name": "div", "props": {"innerHTML": "2,345", "style": {"fontSize": "32px", "fontWeight": "bold"}}}]}]}]}]}, {"id": "content-row", "name": "ElRow", "props": {"gutter": 20}, "children": [{"id": "chart-col", "name": "ElCol", "props": {"span": 16}, "children": [{"id": "chart-card", "name": "ElCard", "props": {"shadow": "never", "style": {"height": "400px"}}, "children": [{"id": "chart-header", "name": "div", "props": {"slot": "header", "style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"id": "chart-title", "name": "span", "props": {"innerHTML": "今日访问趋势", "style": {"fontSize": "16px", "fontWeight": "bold", "color": "#2c3e50"}}}]}, {"id": "chart-container", "name": "div", "props": {"style": {"height": "320px", "display": "flex", "alignItems": "center", "justifyContent": "center", "background": "#f8f9fa", "border": "2px dashed #dee2e6", "borderRadius": "8px"}}, "children": [{"id": "chart-placeholder", "name": "div", "props": {"innerHTML": "📊 图表区域<br><small>可在此处集成 ECharts 或其他图表库</small>", "style": {"textAlign": "center", "color": "#6c757d", "fontSize": "16px"}}}]}]}]}, {"id": "form-col", "name": "ElCol", "props": {"span": 8}, "children": [{"id": "form-card", "name": "ElCard", "props": {"shadow": "never", "style": {"height": "400px"}}, "children": [{"id": "form-header", "name": "div", "props": {"slot": "header", "innerHTML": "数据录入", "style": {"fontSize": "16px", "fontWeight": "bold", "color": "#2c3e50"}}}, {"id": "data-form", "name": "ElForm", "props": {"labelWidth": "80px", "style": {"padding": "20px 0"}}, "children": [{"id": "form-item-name", "name": "ElFormItem", "props": {"label": "数据名称"}, "children": [{"id": "input-name", "name": "ElInput", "props": {"placeholder": "请输入数据名称"}}]}, {"id": "form-item-type", "name": "ElFormItem", "props": {"label": "数据类型"}, "children": [{"id": "select-type", "name": "ElSelect", "props": {"placeholder": "请选择数据类型", "style": {"width": "100%"}}, "children": [{"id": "option-1", "name": "ElOption", "props": {"label": "访问数据", "value": "visit"}}, {"id": "option-2", "name": "ElOption", "props": {"label": "销售数据", "value": "sales"}}, {"id": "option-3", "name": "ElOption", "props": {"label": "用户数据", "value": "user"}}]}]}, {"id": "form-item-value", "name": "ElFormItem", "props": {"label": "数值"}, "children": [{"id": "input-value", "name": "ElInputNumber", "props": {"placeholder": "请输入数值", "style": {"width": "100%"}}}]}, {"id": "form-item-buttons", "name": "ElFormItem", "props": {"style": {"marginTop": "20px", "textAlign": "center"}}, "children": [{"id": "submit-button", "name": "ElButton", "props": {"type": "primary", "innerHTML": "提交数据", "style": {"marginRight": "10px"}}}, {"id": "reset-button", "name": "ElButton", "props": {"innerHTML": "重置"}}]}]}]}]}]}]}]}]}