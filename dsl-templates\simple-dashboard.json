{
  "id": "simple-dashboard",
  "name": "简化数据统计页面",
  "platform": "web",
  "version": "1.0.0",
  "description": "简化版数据统计页面，确保所有组件正常显示",
  "children": [
    {
      "id": "main-container",
      "name": "div",
      "props": {
        "style": {
          "display": "flex",
          "height": "100vh",
          "background": "#f5f5f5"
        }
      },
      "children": [
        {
          "id": "sidebar",
          "name": "div",
          "props": {
            "style": {
              "width": "200px",
              "background": "#2c3e50",
              "color": "#fff",
              "padding": "20px"
            }
          },
          "children": [
            {
              "id": "sidebar-title",
              "name": "h3",
              "props": {
                "innerHTML": "数据统计管理系统",
                "style": {
                  "color": "#fff",
                  "borderBottom": "1px solid #34495e",
                  "paddingBottom": "15px",
                  "marginBottom": "20px"
                }
              }
            },
            {
              "id": "menu-item-1",
              "name": "div",
              "props": {
                "innerHTML": "📊 仪表盘",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "background": "#3498db",
                  "paddingLeft": "10px"
                }
              }
            },
            {
              "id": "menu-item-2",
              "name": "div",
              "props": {
                "innerHTML": "👥 用户管理",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "paddingLeft": "10px"
                }
              }
            },
            {
              "id": "menu-item-3",
              "name": "div",
              "props": {
                "innerHTML": "📦 订单管理",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "paddingLeft": "10px"
                }
              }
            },
            {
              "id": "menu-item-4",
              "name": "div",
              "props": {
                "innerHTML": "🛍️ 商品管理",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "paddingLeft": "10px"
                }
              }
            },
            {
              "id": "menu-item-5",
              "name": "div",
              "props": {
                "innerHTML": "💰 财务管理",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "paddingLeft": "10px"
                }
              }
            },
            {
              "id": "menu-item-6",
              "name": "div",
              "props": {
                "innerHTML": "📈 报表分析",
                "style": {
                  "padding": "10px 0",
                  "cursor": "pointer",
                  "borderRadius": "4px",
                  "marginBottom": "5px",
                  "paddingLeft": "10px"
                }
              }
            }
          ]
        },
        {
          "id": "main-content",
          "name": "div",
          "props": {
            "style": {
              "flex": "1",
              "padding": "20px",
              "background": "#fff",
              "overflow": "auto"
            }
          },
          "children": [
            {
              "id": "page-title",
              "name": "h2",
              "props": {
                "innerHTML": "数据统计概览",
                "style": {
                  "margin": "0 0 20px 0",
                  "color": "#2c3e50",
                  "fontSize": "24px",
                  "borderBottom": "1px solid #e0e0e0",
                  "paddingBottom": "15px"
                }
              },
            {
              "id": "stats-cards-container",
              "name": "div",
              "props": {
                "style": {
                  "display": "flex",
                  "gap": "20px",
                  "marginBottom": "30px"
                }
              },
              "children": [
                {
                  "id": "stat-card-1",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "1",
                      "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                      "color": "#fff",
                      "padding": "30px 20px",
                      "borderRadius": "10px",
                      "textAlign": "center",
                      "boxShadow": "0 4px 6px rgba(0,0,0,0.1)"
                    }
                  },
                  "children": [
                    {
                      "id": "card-1-icon",
                      "name": "div",
                      "props": {
                        "innerHTML": "📊",
                        "style": {
                          "fontSize": "40px",
                          "marginBottom": "10px"
                        }
                      }
                    },
                    {
                      "id": "card-1-title",
                      "name": "div",
                      "props": {
                        "innerHTML": "今日访问",
                        "style": {
                          "fontSize": "14px",
                          "marginBottom": "5px",
                          "opacity": "0.9"
                        }
                      }
                    },
                    {
                      "id": "card-1-value",
                      "name": "div",
                      "props": {
                        "innerHTML": "1,234",
                        "style": {
                          "fontSize": "32px",
                          "fontWeight": "bold"
                        }
                      }
                    }
                  ]
                },
                {
                  "id": "stat-card-2",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "1",
                      "background": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                      "color": "#fff",
                      "padding": "30px 20px",
                      "borderRadius": "10px",
                      "textAlign": "center",
                      "boxShadow": "0 4px 6px rgba(0,0,0,0.1)"
                    }
                  },
                  "children": [
                    {
                      "id": "card-2-icon",
                      "name": "div",
                      "props": {
                        "innerHTML": "💰",
                        "style": {
                          "fontSize": "40px",
                          "marginBottom": "10px"
                        }
                      }
                    },
                    {
                      "id": "card-2-title",
                      "name": "div",
                      "props": {
                        "innerHTML": "销售额",
                        "style": {
                          "fontSize": "14px",
                          "marginBottom": "5px",
                          "opacity": "0.9"
                        }
                      }
                    },
                    {
                      "id": "card-2-value",
                      "name": "div",
                      "props": {
                        "innerHTML": "¥56,789",
                        "style": {
                          "fontSize": "32px",
                          "fontWeight": "bold"
                        }
                      }
                    }
                  ]
                },
                {
                  "id": "stat-card-3",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "1",
                      "background": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                      "color": "#fff",
                      "padding": "30px 20px",
                      "borderRadius": "10px",
                      "textAlign": "center",
                      "boxShadow": "0 4px 6px rgba(0,0,0,0.1)"
                    }
                  },
                  "children": [
                    {
                      "id": "card-3-icon",
                      "name": "div",
                      "props": {
                        "innerHTML": "👥",
                        "style": {
                          "fontSize": "40px",
                          "marginBottom": "10px"
                        }
                      }
                    },
                    {
                      "id": "card-3-title",
                      "name": "div",
                      "props": {
                        "innerHTML": "用户数",
                        "style": {
                          "fontSize": "14px",
                          "marginBottom": "5px",
                          "opacity": "0.9"
                        }
                      }
                    },
                    {
                      "id": "card-3-value",
                      "name": "div",
                      "props": {
                        "innerHTML": "8,456",
                        "style": {
                          "fontSize": "32px",
                          "fontWeight": "bold"
                        }
                      }
                    }
                  ]
                },
                {
                  "id": "stat-card-4",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "1",
                      "background": "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
                      "color": "#fff",
                      "padding": "30px 20px",
                      "borderRadius": "10px",
                      "textAlign": "center",
                      "boxShadow": "0 4px 6px rgba(0,0,0,0.1)"
                    }
                  },
                  "children": [
                    {
                      "id": "card-4-icon",
                      "name": "div",
                      "props": {
                        "innerHTML": "📦",
                        "style": {
                          "fontSize": "40px",
                          "marginBottom": "10px"
                        }
                      }
                    },
                    {
                      "id": "card-4-title",
                      "name": "div",
                      "props": {
                        "innerHTML": "订单数",
                        "style": {
                          "fontSize": "14px",
                          "marginBottom": "5px",
                          "opacity": "0.9"
                        }
                      }
                    },
                    {
                      "id": "card-4-value",
                      "name": "div",
                      "props": {
                        "innerHTML": "2,345",
                        "style": {
                          "fontSize": "32px",
                          "fontWeight": "bold"
                        }
                      }
                    }
                  ]
                }
              ]
            },
            {
              "id": "content-area",
              "name": "div",
              "props": {
                "style": {
                  "display": "flex",
                  "gap": "20px"
                }
              },
              "children": [
                {
                  "id": "chart-area",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "2",
                      "background": "#fff",
                      "border": "1px solid #e0e0e0",
                      "borderRadius": "8px",
                      "padding": "20px"
                    }
                  },
                  "children": [
                    {
                      "id": "chart-title",
                      "name": "h3",
                      "props": {
                        "innerHTML": "今日访问趋势",
                        "style": {
                          "margin": "0 0 20px 0",
                          "color": "#2c3e50",
                          "fontSize": "16px",
                          "fontWeight": "bold"
                        }
                      }
                    },
                    {
                      "id": "chart-placeholder",
                      "name": "div",
                      "props": {
                        "style": {
                          "height": "300px",
                          "background": "#f8f9fa",
                          "border": "2px dashed #dee2e6",
                          "borderRadius": "8px",
                          "display": "flex",
                          "alignItems": "center",
                          "justifyContent": "center",
                          "textAlign": "center",
                          "color": "#6c757d",
                          "fontSize": "16px"
                        }
                      },
                      "children": [
                        {
                          "id": "chart-text",
                          "name": "div",
                          "props": {
                            "innerHTML": "📊 图表区域<br><small>可在此处集成 ECharts 或其他图表库</small>"
                          }
                        }
                      ]
                    }
                  ]
                },
                {
                  "id": "form-area",
                  "name": "div",
                  "props": {
                    "style": {
                      "flex": "1",
                      "background": "#fff",
                      "border": "1px solid #e0e0e0",
                      "borderRadius": "8px",
                      "padding": "20px"
                    }
                  },
                  "children": [
                    {
                      "id": "form-title",
                      "name": "h3",
                      "props": {
                        "innerHTML": "数据录入",
                        "style": {
                          "margin": "0 0 20px 0",
                          "color": "#2c3e50",
                          "fontSize": "16px",
                          "fontWeight": "bold"
                        }
                      }
                    },
                    {
                      "id": "form-field-1",
                      "name": "div",
                      "props": {
                        "style": {
                          "marginBottom": "15px"
                        }
                      },
                      "children": [
                        {
                          "id": "label-1",
                          "name": "label",
                          "props": {
                            "innerHTML": "数据名称:",
                            "style": {
                              "display": "block",
                              "marginBottom": "5px",
                              "color": "#333",
                              "fontSize": "14px"
                            }
                          }
                        },
                        {
                          "id": "input-1",
                          "name": "input",
                          "props": {
                            "type": "text",
                            "placeholder": "请输入数据名称",
                            "style": {
                              "width": "100%",
                              "padding": "8px 12px",
                              "border": "1px solid #ddd",
                              "borderRadius": "4px",
                              "fontSize": "14px"
                            }
                          }
                        }
                      ]
                    },
                    {
                      "id": "form-field-2",
                      "name": "div",
                      "props": {
                        "style": {
                          "marginBottom": "15px"
                        }
                      },
                      "children": [
                        {
                          "id": "label-2",
                          "name": "label",
                          "props": {
                            "innerHTML": "数据类型:",
                            "style": {
                              "display": "block",
                              "marginBottom": "5px",
                              "color": "#333",
                              "fontSize": "14px"
                            }
                          }
                        },
                        {
                          "id": "select-1",
                          "name": "select",
                          "props": {
                            "style": {
                              "width": "100%",
                              "padding": "8px 12px",
                              "border": "1px solid #ddd",
                              "borderRadius": "4px",
                              "fontSize": "14px"
                            }
                          },
                          "children": [
                            {
                              "id": "option-1",
                              "name": "option",
                              "props": {
                                "value": "visit",
                                "innerHTML": "访问数据"
                              }
                            },
                            {
                              "id": "option-2",
                              "name": "option",
                              "props": {
                                "value": "sales",
                                "innerHTML": "销售数据"
                              }
                            },
                            {
                              "id": "option-3",
                              "name": "option",
                              "props": {
                                "value": "user",
                                "innerHTML": "用户数据"
                              }
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "id": "form-field-3",
                      "name": "div",
                      "props": {
                        "style": {
                          "marginBottom": "20px"
                        }
                      },
                      "children": [
                        {
                          "id": "label-3",
                          "name": "label",
                          "props": {
                            "innerHTML": "数值:",
                            "style": {
                              "display": "block",
                              "marginBottom": "5px",
                              "color": "#333",
                              "fontSize": "14px"
                            }
                          }
                        },
                        {
                          "id": "input-3",
                          "name": "input",
                          "props": {
                            "type": "number",
                            "placeholder": "请输入数值",
                            "style": {
                              "width": "100%",
                              "padding": "8px 12px",
                              "border": "1px solid #ddd",
                              "borderRadius": "4px",
                              "fontSize": "14px"
                            }
                          }
                        }
                      ]
                    },
                    {
                      "id": "form-buttons",
                      "name": "div",
                      "props": {
                        "style": {
                          "textAlign": "center"
                        }
                      },
                      "children": [
                        {
                          "id": "submit-btn",
                          "name": "button",
                          "props": {
                            "innerHTML": "提交数据",
                            "style": {
                              "background": "#409eff",
                              "color": "#fff",
                              "border": "none",
                              "padding": "10px 20px",
                              "borderRadius": "4px",
                              "marginRight": "10px",
                              "cursor": "pointer",
                              "fontSize": "14px"
                            }
                          }
                        },
                        {
                          "id": "reset-btn",
                          "name": "button",
                          "props": {
                            "innerHTML": "重置",
                            "style": {
                              "background": "#f56c6c",
                              "color": "#fff",
                              "border": "none",
                              "padding": "10px 20px",
                              "borderRadius": "4px",
                              "cursor": "pointer",
                              "fontSize": "14px"
                            }
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
