package com.vtj.backend.repository;

import com.vtj.backend.entity.Schema;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Schema 数据访问层
 */
@Repository
public interface SchemaRepository extends JpaRepository<Schema, Long> {

    /**
     * 根据应用、类型和名称查找
     */
    Optional<Schema> findByAppAndTypeAndName(String app, String type, String name);

    /**
     * 根据应用和类型查找列表
     */
    List<Schema> findByAppAndType(String app, String type);

    /**
     * 根据应用、类型和名称模糊查询
     */
    @Query("SELECT s FROM Schema s WHERE s.app = :app AND s.type = :type AND s.name LIKE %:name%")
    List<Schema> findByAppAndTypeAndNameLike(@Param("app") String app, 
                                              @Param("type") String type, 
                                              @Param("name") String name);

    /**
     * 搜索 Schema - 支持多条件查询
     */
    @Query("SELECT s FROM Schema s WHERE " +
           "(:app IS NULL OR s.app = :app) AND " +
           "(:type IS NULL OR s.type = :type) AND " +
           "(:name IS NULL OR s.name LIKE %:name%) AND " +
           "(:keyword IS NULL OR s.name LIKE %:keyword% OR s.description LIKE %:keyword%)")
    List<Schema> searchSchemas(@Param("app") String app,
                              @Param("type") String type,
                              @Param("name") String name,
                              @Param("keyword") String keyword);

    /**
     * 根据ID列表批量删除
     */
    void deleteByIdIn(List<Long> ids);

    /**
     * 删除指定的 Schema
     */
    void deleteByAppAndTypeAndNameIn(String app, String type, List<String> names);
}