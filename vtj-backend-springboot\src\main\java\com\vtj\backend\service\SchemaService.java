package com.vtj.backend.service;

import com.vtj.backend.dto.ParseVueDto;
import com.vtj.backend.dto.SchemaDto;

import java.util.List;
import java.util.Map;

/**
 * Schema 服务接口
 */
public interface SchemaService {

    /**
     * 保存 Schema
     */
    Map<String, Object> save(SchemaDto dto);

    /**
     * 获取单个 Schema
     */
    Map<String, Object> findOne(String app, String type, String name);

    /**
     * 查询 Schema 列表
     */
    List<Map<String, Object>> find(String app, String type, String name);

    /**
     * 搜索 Schema
     */
    List<Map<String, Object>> search(String app, String type, String name, String keyword);

    /**
     * 根据ID获取单个 Schema
     */
    Map<String, Object> findOneById(String id);

    /**
     * 删除 Schema
     */
    void remove(String app, String type, List<String> names);

    /**
     * 根据ID批量删除 Schema
     */
    void removeByIds(List<String> ids);

    /**
     * 生成 Vue 代码
     */
    String generateVue(String app, String platform, Map<String, Object> dsl);

    /**
     * 解析 Vue 代码
     */
    Map<String, Object> parseVue(ParseVueDto dto);

    /**
     * 项目出码 - 生成完整项目代码包
     */
    Map<String, Object> generateProject(String app, Map<String, Object> project);
}