package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI对话记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "chats")
public class Chat extends BaseEntity {

    /**
     * 对话ID
     */
    @Column(name = "chat_id", nullable = false, unique = true, length = 50)
    private String chatId;

    /**
     * 话题ID
     */
    @Column(name = "topic_id", nullable = false, length = 50)
    private String topicId;

    /**
     * 角色: user/assistant
     */
    @Column(name = "role", nullable = false, length = 20)
    private String role;

    /**
     * 对话内容
     */
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * Token消耗
     */
    @Column(name = "tokens")
    private Integer tokens = 0;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status = 1;
}
