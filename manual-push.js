// 手动推送简单的DSL到VTJ平台
import fetch from 'node-fetch';

// 创建简单的用户管理DSL
const simpleDSL = {
  id: "123",
  componentName: "UserManagement",
  type: "block",
  props: {},
  children: [
    {
      id: "header",
      componentName: "div",
      type: "element",
      props: {
        className: "page-header",
        style: {
          textAlign: "center",
          padding: "20px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
          borderRadius: "8px",
          marginBottom: "20px"
        }
      },
      children: [
        {
          id: "title",
          componentName: "h1",
          type: "element",
          props: {},
          children: ["👥 用户管理系统"]
        },
        {
          id: "subtitle",
          componentName: "p",
          type: "element",
          props: {},
          children: ["管理系统用户信息，支持增删改查操作"]
        }
      ]
    },
    {
      id: "stats",
      componentName: "div",
      type: "element",
      props: {
        className: "stats-container",
        style: {
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: "20px",
          marginBottom: "20px"
        }
      },
      children: [
        {
          id: "stat1",
          componentName: "div",
          type: "element",
          props: {
            style: {
              background: "#f8f9fa",
              padding: "20px",
              borderRadius: "8px",
              textAlign: "center",
              border: "1px solid #e9ecef"
            }
          },
          children: [
            {
              id: "stat1-title",
              componentName: "h3",
              type: "element",
              props: { style: { margin: "0 0 10px 0", color: "#495057" } },
              children: ["总用户数"]
            },
            {
              id: "stat1-value",
              componentName: "div",
              type: "element",
              props: { style: { fontSize: "24px", fontWeight: "bold", color: "#007bff" } },
              children: ["1,234"]
            }
          ]
        },
        {
          id: "stat2",
          componentName: "div",
          type: "element",
          props: {
            style: {
              background: "#f8f9fa",
              padding: "20px",
              borderRadius: "8px",
              textAlign: "center",
              border: "1px solid #e9ecef"
            }
          },
          children: [
            {
              id: "stat2-title",
              componentName: "h3",
              type: "element",
              props: { style: { margin: "0 0 10px 0", color: "#495057" } },
              children: ["活跃用户"]
            },
            {
              id: "stat2-value",
              componentName: "div",
              type: "element",
              props: { style: { fontSize: "24px", fontWeight: "bold", color: "#28a745" } },
              children: ["987"]
            }
          ]
        },
        {
          id: "stat3",
          componentName: "div",
          type: "element",
          props: {
            style: {
              background: "#f8f9fa",
              padding: "20px",
              borderRadius: "8px",
              textAlign: "center",
              border: "1px solid #e9ecef"
            }
          },
          children: [
            {
              id: "stat3-title",
              componentName: "h3",
              type: "element",
              props: { style: { margin: "0 0 10px 0", color: "#495057" } },
              children: ["新增用户"]
            },
            {
              id: "stat3-value",
              componentName: "div",
              type: "element",
              props: { style: { fontSize: "24px", fontWeight: "bold", color: "#ffc107" } },
              children: ["56"]
            }
          ]
        }
      ]
    },
    {
      id: "content",
      componentName: "div",
      type: "element",
      props: {
        style: {
          background: "white",
          padding: "20px",
          borderRadius: "8px",
          border: "1px solid #e9ecef"
        }
      },
      children: [
        {
          id: "content-title",
          componentName: "h2",
          type: "element",
          props: { style: { marginTop: "0" } },
          children: ["用户列表"]
        },
        {
          id: "content-desc",
          componentName: "p",
          type: "element",
          props: {},
          children: ["这里将显示用户管理功能，包括搜索、添加、编辑和删除用户等操作。"]
        }
      ]
    }
  ]
};

// 推送到VTJ平台
const requestData = {
  name: "123",
  content: JSON.stringify(simpleDSL),
  description: "用户管理系统 - 手动创建的DSL"
};

fetch('http://localhost:8080/api/schemas/asdfasdf/page', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('推送结果:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('推送失败:', error);
});
