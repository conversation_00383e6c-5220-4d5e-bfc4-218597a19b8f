# 页面保存问题修复方案

## 问题分析

通过API测试发现：
1. 页面DSL保存成功（test-page已保存）
2. 项目DSL为空对象（缺少页面列表信息）
3. VTJ引擎依赖项目DSL中的pages数组来显示页面列表

## 根本原因

新增页面时，只保存了页面DSL，但没有更新项目DSL中的pages数组，导致：
- 页面数据存在数据库中
- 但项目不知道有这个页面
- 刷新后页面列表为空

## 修复步骤

### 1. 初始化项目DSL

首先需要为测试应用创建正确的项目DSL：

```bash
curl -X POST "http://localhost:8080/api/schemas/test-app/project" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-app",
    "content": "{\"id\":\"test-app\",\"name\":\"测试应用\",\"platform\":\"web\",\"pages\":[],\"blocks\":[],\"config\":{\"title\":\"测试应用\"}}"
  }'
```

### 2. 修复LcdpService的saveFile方法

需要在保存页面后，同时更新项目DSL：

```typescript
async saveFile(file: BlockSchema, project?: ProjectSchema): Promise<boolean> {
  // ... 现有的保存逻辑 ...
  
  // 保存成功后，更新项目DSL
  if (ret && project) {
    await this.updateProjectPages(file, project);
  }
  
  return !!ret;
}

private async updateProjectPages(file: BlockSchema, project: ProjectSchema): Promise<void> {
  try {
    // 获取当前项目DSL
    const currentProject = await this.getProjectDSL(project.id);
    
    // 检查页面是否已存在
    const pageExists = currentProject.pages?.some(p => p.id === file.id);
    
    if (!pageExists) {
      // 添加页面到项目
      if (!currentProject.pages) {
        currentProject.pages = [];
      }
      
      currentProject.pages.push({
        id: file.id,
        name: (file as any).title || file.id,
        path: `/${file.id}`
      });
      
      // 保存更新后的项目DSL
      await this.saveProject(currentProject);
    }
  } catch (error) {
    console.warn('更新项目页面列表失败:', error);
  }
}
```

### 3. 临时修复方案

对于当前的问题，可以手动修复项目DSL：

```bash
# 获取所有页面
curl "http://localhost:8080/api/schemas/test-app/file"

# 更新项目DSL，包含页面信息
curl -X POST "http://localhost:8080/api/schemas/test-app/project" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-app",
    "content": "{\"id\":\"test-app\",\"name\":\"测试应用\",\"platform\":\"web\",\"pages\":[{\"id\":\"test-page\",\"name\":\"测试页面\",\"path\":\"/test-page\"}],\"blocks\":[],\"config\":{\"title\":\"测试应用\"}}"
  }'
```

## 长期解决方案

1. **完善Service层**：确保页面保存时同步更新项目DSL
2. **添加事务支持**：保证页面和项目DSL的一致性
3. **优化错误处理**：提供更好的错误提示
4. **添加数据校验**：确保数据完整性

## 验证修复

修复后应该能够：
1. 新增页面后立即在页面列表中看到
2. 刷新页面后页面仍然存在
3. 页面可以正常打开和编辑