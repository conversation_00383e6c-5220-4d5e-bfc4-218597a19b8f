package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "settings")
public class Setting extends BaseEntity {

    /**
     * 配置代码
     */
    @Column(name = "code", nullable = false, unique = true, length = 50)
    private String code;

    /**
     * 配置名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 配置值
     */
    @Column(name = "value", columnDefinition = "TEXT")
    private String value;

    /**
     * 配置类型
     */
    @Column(name = "type", length = 20)
    private String type = "string";

    /**
     * 配置描述
     */
    @Column(name = "description", length = 200)
    private String description;
}
