package com.vtj.backend.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * API 根路径控制器
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class ApiController {

    /**
     * API 根路径接口 - 处理根路径访问
     */
    @GetMapping("/")
    public ResponseEntity<Map<String, Object>> apiRoot() {
        log.info("API 根路径访问");
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "VTJ Backend API");
        response.put("version", "1.0.0");
        response.put("timestamp", System.currentTimeMillis());
        response.put("status", "running");
        
        return ResponseEntity.ok(response);
    }

    /**
     * API 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        log.info("API 健康检查");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("status", "healthy");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 处理重复路径的兼容接口 - /api/api/open/report
     */
    @GetMapping("/api/open/report")
    public ResponseEntity<Map<String, Object>> duplicatePathReport() {
        log.warn("检测到重复路径请求: /api/api/open/report，已重定向处理");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "报告接口正常");
        response.put("timestamp", System.currentTimeMillis());
        response.put("note", "检测到重复路径，已自动处理");

        return ResponseEntity.ok(response);
    }
}
