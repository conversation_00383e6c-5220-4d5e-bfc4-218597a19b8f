import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testVueToDsl() {
  try {
    // 读取Vue文件内容
    const vueFilePath = path.join(__dirname, 'vtj-sync-tool/src/pages/123.vue');
    const vueContent = fs.readFileSync(vueFilePath, 'utf-8');

    console.log('📄 Vue文件内容长度:', vueContent.length);
    console.log('📄 Vue文件前100个字符:', vueContent.substring(0, 100));

    // 调用转换API
    const response = await fetch('http://localhost:3001/api/parser/vue', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source: vueContent,
        id: '123',
        name: '123'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    console.log('🔄 转换结果状态:', result.success);
    console.log('🔄 转换结果DSL长度:', result.dsl ? JSON.stringify(result.dsl).length : 0);
    console.log('🔄 转换结果DSL:', JSON.stringify(result.dsl, null, 2));

    if (result.error) {
      console.error('❌ 转换错误:', result.error);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testVueToDsl();
