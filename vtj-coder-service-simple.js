import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'vtj-coder-service-simple',
    timestamp: new Date().toISOString()
  });
});

/**
 * Vue 代码转 DSL
 * POST /api/parser/vue
 */
app.post('/api/parser/vue', async (req, res) => {
  try {
    const { id, name, source, project } = req.body;
    
    console.log('收到 Vue 转 DSL 请求:', {
      id,
      name,
      hasSource: !!source,
      sourceLength: source ? source.length : 0,
      hasProject: !!project
    });

    if (!source) {
      throw new Error('Vue源码不能为空');
    }

    // 解析Vue文件
    const template = extractTemplate(source);
    const script = extractScript(source);
    const style = extractStyle(source);
    
    // 从template中提取组件结构
    const components = parseTemplate(template);
    
    // 构建DSL
    const dsl = {
      id: id || name,
      componentName: name,
      type: 'block',
      props: {},
      children: components,
      meta: {
        title: `${name} 页面`,
        description: '通过本地同步工具创建的页面',
        generated: false,
        timestamp: Date.now(),
        source: {
          template: template,
          script: script,
          style: style
        }
      }
    };

    console.log('Vue 转 DSL 成功:', {
      id: dsl.id,
      type: dsl.type,
      childrenCount: dsl.children.length,
      hasTemplate: !!template,
      hasScript: !!script,
      hasStyle: !!style
    });

    res.json({
      success: true,
      dsl: dsl,
      message: 'Vue 转 DSL 成功'
    });

  } catch (error) {
    console.error('Vue 转 DSL 失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Vue 转 DSL 失败'
    });
  }
});

/**
 * DSL 转 Vue 代码
 * POST /api/generator/vue
 */
app.post('/api/generator/vue', async (req, res) => {
  try {
    const { dsl, platform = 'web' } = req.body;
    
    console.log('收到 DSL 转 Vue 请求:', {
      platform,
      hasDsl: !!dsl,
      dslType: dsl?.type
    });

    // 生成Vue代码
    const vueCode = generateVueFromDsl(dsl);

    res.json({
      success: true,
      code: vueCode,
      message: 'DSL 转 Vue 成功'
    });

  } catch (error) {
    console.error('DSL 转 Vue 失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'DSL 转 Vue 失败'
    });
  }
});

// 提取template部分
function extractTemplate(vueContent) {
  const match = vueContent.match(/<template[^>]*>([\s\S]*?)<\/template>/);
  return match ? match[1].trim() : '';
}

// 提取script部分
function extractScript(vueContent) {
  const match = vueContent.match(/<script[^>]*>([\s\S]*?)<\/script>/);
  return match ? match[1].trim() : '';
}

// 提取style部分
function extractStyle(vueContent) {
  const match = vueContent.match(/<style[^>]*>([\s\S]*?)<\/style>/);
  return match ? match[1].trim() : '';
}

// 简单解析template为组件结构
function parseTemplate(template) {
  if (!template) return [];
  
  // 简化处理：将整个template作为一个HTML组件
  return [{
    id: 'root',
    componentName: 'div',
    type: 'element',
    props: {
      innerHTML: template
    },
    children: []
  }];
}

// 从DSL生成Vue代码
function generateVueFromDsl(dsl) {
  if (!dsl) return '<template><div>空页面</div></template>';
  
  // 如果有原始源码，直接返回
  if (dsl.meta?.source) {
    const { template, script, style } = dsl.meta.source;
    return `<template>${template}</template>
${script ? `<script>${script}</script>` : ''}
${style ? `<style>${style}</style>` : ''}`;
  }
  
  // 否则从组件结构生成
  const template = generateTemplateFromComponents(dsl.children || []);
  return `<template>${template}</template>`;
}

// 从组件结构生成template
function generateTemplateFromComponents(components) {
  if (!components || components.length === 0) {
    return '<div>空页面</div>';
  }
  
  return components.map(comp => {
    if (comp.props?.innerHTML) {
      return comp.props.innerHTML;
    }
    return `<${comp.componentName || 'div'}></${comp.componentName || 'div'}>`;
  }).join('\n');
}

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: error.message,
    message: '服务器内部错误'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 VTJ 简化代码转换服务启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📝 DSL转Vue: POST http://localhost:${PORT}/api/generator/vue`);
  console.log(`🔄 Vue转DSL: POST http://localhost:${PORT}/api/parser/vue`);
});
