{"name": "vtj-lcdp-demo", "description": "VTJ低代码在线开发平台示例", "private": false, "version": "0.12.0", "type": "module", "scripts": {"setup": "npm install --unsafe-perm --registry=https://registry.npmmirror.com", "dev": "cross-env ENV_TYPE=local vite", "sit:main": "vue-tsc && cross-env ENV_TYPE=sit BUILD_TYPE=main vite build", "sit:web": "vue-tsc && cross-env ENV_TYPE=sit BUILD_TYPE=web vite build", "sit:h5": "vue-tsc && cross-env ENV_TYPE=sit BUILD_TYPE=h5 vite build", "sit:uniapp": "vue-tsc && cross-env ENV_TYPE=sit BUILD_TYPE=uniapp vite build", "sit": "npm run sit:main && npm run sit:web && npm run sit:h5 && npm run sit:uniapp", "build:main": "vue-tsc && cross-env ENV_TYPE=live BUILD_TYPE=main vite build", "build:web": "vue-tsc && cross-env ENV_TYPE=live BUILD_TYPE=web vite build", "build:h5": "vue-tsc && cross-env ENV_TYPE=live BUILD_TYPE=h5 vite build", "build:uniapp": "vue-tsc && cross-env ENV_TYPE=live BUILD_TYPE=uniapp vite build", "build": "npm run build:main && npm run build:web && npm run build:h5 && npm run build:uniapp", "preview": "vite preview", "clean": "node ./scripts/clean.mjs", "ssh": "node scripts/ssh.mjs"}, "dependencies": {"@dcloudio/uni-h5-vue": "3.0.0-4050720250324001", "@vtj/h5": "latest", "@vtj/materials": "^0.12.64", "@vtj/uni": "latest", "@vtj/web": "latest", "cors": "^2.8.5", "express": "^5.1.0", "node-fetch": "^3.3.2", "vue": "~3.5.0", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "latest", "@vtj/pro": "latest", "node-ssh": "~13.2.0"}, "files": ["dist", "package.json", "README.md"]}