package com.vtj.backend.service.impl;

import com.vtj.backend.entity.Setting;
import com.vtj.backend.repository.SettingRepository;
import com.vtj.backend.service.SettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 系统设置服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SettingServiceImpl implements SettingService {

    private final SettingRepository settingRepository;

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getSetting(String code) {
        Optional<Setting> settingOpt = settingRepository.findByCode(code);
        if (settingOpt.isPresent()) {
            return entityToMap(settingOpt.get());
        }
        
        // 返回默认设置
        Map<String, Object> defaultSetting = new HashMap<>();
        defaultSetting.put("code", code);
        defaultSetting.put("name", "未知配置");
        defaultSetting.put("value", "");
        defaultSetting.put("type", "string");
        defaultSetting.put("description", "");
        return defaultSetting;
    }

    @Override
    @Transactional
    public void saveSetting(String code, String name, String value, String type, String description) {
        Optional<Setting> settingOpt = settingRepository.findByCode(code);
        
        Setting setting;
        if (settingOpt.isPresent()) {
            setting = settingOpt.get();
            setting.setName(name);
            setting.setValue(value);
            setting.setType(type != null ? type : "string");
            setting.setDescription(description);
        } else {
            setting = new Setting();
            setting.setCode(code);
            setting.setName(name);
            setting.setValue(value);
            setting.setType(type != null ? type : "string");
            setting.setDescription(description);
        }

        settingRepository.save(setting);
        log.info("保存设置: {} = {}", code, value);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAllSettings() {
        List<Setting> settings = settingRepository.findAll();
        return settings.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional
    public void deleteSetting(String code) {
        settingRepository.deleteByCode(code);
        log.info("删除设置: {}", code);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getDict(String code) {
        List<Map<String, Object>> dict = new ArrayList<>();
        
        switch (code) {
            case "platform":
                dict.add(Map.of("label", "Web", "value", "web"));
                dict.add(Map.of("label", "Mobile", "value", "mobile"));
                dict.add(Map.of("label", "Desktop", "value", "desktop"));
                break;
            case "category":
                dict.add(Map.of("label", "管理后台", "value", "admin"));
                dict.add(Map.of("label", "电商", "value", "ecommerce"));
                dict.add(Map.of("label", "企业官网", "value", "corporate"));
                dict.add(Map.of("label", "移动应用", "value", "mobile"));
                break;
            case "ai_model":
                dict.add(Map.of("label", "GPT-3.5 Turbo", "value", "gpt-3.5-turbo"));
                dict.add(Map.of("label", "GPT-4", "value", "gpt-4"));
                dict.add(Map.of("label", "GPT-4 Vision", "value", "gpt-4-vision"));
                break;
            case "topic_type":
                dict.add(Map.of("label", "文本", "value", "text"));
                dict.add(Map.of("label", "图片", "value", "image"));
                dict.add(Map.of("label", "JSON", "value", "json"));
                break;
            default:
                // 可以从数据库或配置文件中获取其他字典数据
                break;
        }
        
        return dict;
    }

    /**
     * 实体转Map
     */
    private Map<String, Object> entityToMap(Setting setting) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", setting.getId());
        map.put("code", setting.getCode());
        map.put("name", setting.getName());
        map.put("value", setting.getValue());
        map.put("type", setting.getType());
        map.put("description", setting.getDescription());
        map.put("createdAt", setting.getCreatedAt());
        map.put("updatedAt", setting.getUpdatedAt());
        return map;
    }
}
