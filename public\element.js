// Element Plus 物料配置
export default {
  name: 'Element Plus',
  version: '2.0.0',
  description: 'Element Plus UI 组件库',
  categories: [
    {
      name: '基础组件',
      key: 'basic',
      components: [
        {
          name: 'Button',
          key: 'el-button',
          icon: 'button',
          description: '按钮组件',
          props: {
            type: {
              type: 'string',
              default: 'default',
              options: ['default', 'primary', 'success', 'warning', 'danger', 'info']
            },
            size: {
              type: 'string',
              default: 'default',
              options: ['large', 'default', 'small']
            },
            disabled: {
              type: 'boolean',
              default: false
            }
          },
          slots: {
            default: {
              type: 'string',
              default: '按钮'
            }
          }
        },
        {
          name: 'Input',
          key: 'el-input',
          icon: 'input',
          description: '输入框组件',
          props: {
            modelValue: {
              type: 'string',
              default: ''
            },
            placeholder: {
              type: 'string',
              default: '请输入'
            },
            disabled: {
              type: 'boolean',
              default: false
            },
            clearable: {
              type: 'boolean',
              default: false
            }
          }
        }
      ]
    },
    {
      name: '布局组件',
      key: 'layout',
      components: [
        {
          name: 'Container',
          key: 'el-container',
          icon: 'container',
          description: '容器组件',
          props: {
            direction: {
              type: 'string',
              default: 'vertical',
              options: ['horizontal', 'vertical']
            }
          },
          slots: {
            default: {
              type: 'slot',
              default: ''
            }
          }
        },
        {
          name: 'Row',
          key: 'el-row',
          icon: 'row',
          description: '行组件',
          props: {
            gutter: {
              type: 'number',
              default: 0
            },
            justify: {
              type: 'string',
              default: 'start',
              options: ['start', 'end', 'center', 'space-around', 'space-between']
            }
          },
          slots: {
            default: {
              type: 'slot',
              default: ''
            }
          }
        },
        {
          name: 'Col',
          key: 'el-col',
          icon: 'col',
          description: '列组件',
          props: {
            span: {
              type: 'number',
              default: 24
            },
            offset: {
              type: 'number',
              default: 0
            }
          },
          slots: {
            default: {
              type: 'slot',
              default: ''
            }
          }
        }
      ]
    }
  ]
};
