import {
  ProjectModel,
  type ProjectSchema,
  type MaterialDescription,
  type BlockSchema,
  type HistorySchema,
  type HistoryItem,
  type ParseVueOptions,
  type PageFile,
  type StaticFileInfo,
  type NodeFromPlugin
} from '@vtj/core';
import { BaseService } from '@vtj/renderer';
import { MANIFEST_JSON, PAGES_JSON } from '@vtj/uni';
import { mapToObject } from '@vtj/utils';
import {
  getSchema,
  getSchemaList,
  saveSchema,
  removeSchema,
  fileGenerator,
  projectGenerator,
  vueParser,
  getLowCodeApp
} from '@/apis';
import { SchemaType } from './types';

export class LcdpService extends BaseService {
  constructor(private notify: (msg: string) => void) {
    super();
  }
  /**
   * 项目初始化，接收引擎传过来的项目启动参数，此处需要判断项目是否存在，如果不存在即创建项目，并返回完整的项目信息
   * @param project
   * @returns
   */
  async init(project: ProjectSchema, isInit?: boolean): Promise<ProjectSchema> {
    const { id: app } = project;

    if (!app) throw new Error(`project id [ ${app} ] is not exist!`);

    // 如果应用运行时，直接查dsl返回
    if (!isInit) {
      // 获取项目DSL
      const res = await getSchema({
        app,
        name: app,
        type: SchemaType.Project
      }).catch(() => null);
      return res?.content as ProjectSchema;
    }

    // 获取项目信息
    const lowcodeApp = await getLowCodeApp(app).catch(() => null);
    if (!lowcodeApp) {
      throw new Error(`app [ ${app} ] is not exist!`);
    }

    // 获取项目DSL
    const res = await getSchema({
      app,
      name: app,
      type: SchemaType.Project
    }).catch(() => null);
    const content = res?.content as ProjectSchema;
    const dsl = content || new ProjectModel(project).toDsl();

    // 同步应用描述
    dsl.name = lowcodeApp.label;
    if (dsl.config?.title) {
      // 为了兼用旧的数据
      dsl.config.title = lowcodeApp.label;
    }

    // 如果uniapp的项目信息缺失 pagesJson 和 manifestJson，填充默认值
    if (dsl.platform === 'uniapp' && !Object.keys(dsl.uniConfig || {}).length) {
      dsl.uniConfig = {
        ...dsl.uniConfig,
        pagesJson: PAGES_JSON,
        manifestJson: MANIFEST_JSON
      };
    }
    // 如果是引擎初始化，需要保存
    await saveSchema({
      app,
      type: SchemaType.Project,
      name: app,
      content: dsl
    });
    return dsl;
  }

  /**
   * 保存项目信息，当项目信息发生变化时，引擎会调用此方法保存
   * @param project
   * @returns
   */
  async saveProject(project: ProjectSchema): Promise<boolean> {
    const model = new ProjectModel(project);
    const ret = await saveSchema({
      type: SchemaType.Project,
      app: model.id,
      name: model.id,
      content: model.toDsl()
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存项目依赖的组件物料信息，在出码时需要用到这些数据， 当项目依赖发生变化时会调用此方法保存数据
   * @param project
   * @param materials
   * @returns
   */
  async saveMaterials(
    project: ProjectSchema,
    materials: Map<string, MaterialDescription>
  ): Promise<boolean> {
    const app = project.id as string;
    const ret = await saveSchema({
      type: SchemaType.Material,
      app: app,
      name: app,
      content: mapToObject(materials)
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存文件DSL， 区块和页面都是相同的方法保存
   * @param file
   * @param project
   * @returns
   */
  async saveFile(file: BlockSchema, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    console.log('🔧 LcdpService.saveFile 开始保存文件:', {
      fileId: file.id,
      app,
      fileType: (file as any).type || 'unknown',
      fileTitle: (file as any).title || 'unknown',
      projectId: project?.id,
      projectName: project?.name
    });

    // 验证必要参数
    if (!app) {
      console.error('❌ LcdpService.saveFile 保存失败: 应用ID为空');
      this.notify('保存文件失败: 应用ID为空');
      return false;
    }

    if (!file.id) {
      console.error('❌ LcdpService.saveFile 保存失败: 文件ID为空');
      this.notify('保存文件失败: 文件ID为空');
      return false;
    }

    const ret = await saveSchema({
      type: SchemaType.File,
      app,
      name: file.id as string,
      content: file
    }).catch((error) => {
      console.error('❌ LcdpService.saveFile 保存失败:', error);
      this.notify(`保存文件失败: ${error.message || error}`);
      return false;
    });

    if (ret) {
      console.log('✅ LcdpService.saveFile 保存成功:', ret);
      
      // 🔧 修复：保存文件后，验证文件是否真的保存成功
      await this.verifyFileSaved(file.id as string, app);
      
      // 🔧 修复：保存文件后，同步更新项目DSL中的页面列表
      if (project) {
        await this.updateProjectPages(file, project).catch((error) => {
          console.warn('⚠️ 更新项目页面列表失败:', error);
        });
      }
      
      // this.notify('页面保存成功！'); // 注释掉成功提示，避免频繁弹窗
    } else {
      console.log('❌ LcdpService.saveFile 保存失败: 返回值为空');
      this.notify('保存文件失败: 服务器返回空数据');
    }

    return !!ret;
  }

  /**
   * 验证文件是否真的保存成功
   * @private
   */
  private async verifyFileSaved(fileId: string, app: string): Promise<void> {
    try {
      console.log('🔍 验证文件保存状态:', { fileId, app });

      // 🔧 修复：增加延迟时间，确保数据库事务完全提交
      await new Promise(resolve => setTimeout(resolve, 500));

      // 🔧 修复：添加重试机制，最多重试3次
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        const res = await getSchema({
          type: SchemaType.File,
          app: app,
          name: fileId
        }).catch(() => null);

        if (res && res.content) {
          console.log('✅ 文件保存验证成功:', {
            fileId,
            hasContent: !!res.content,
            retryCount
          });
          return; // 验证成功，直接返回
        }

        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`🔄 文件保存验证重试 (${retryCount}/${maxRetries}):`, { fileId });
          // 递增延迟：200ms, 400ms, 600ms
          await new Promise(resolve => setTimeout(resolve, 200 * retryCount));
        }
      }

      // 所有重试都失败了
      console.warn('⚠️ 文件保存验证失败，但不影响主流程:', { fileId, app, retryCount });

    } catch (error) {
      console.error('❌ 文件保存验证异常:', error);
      // 不抛出异常，避免影响主流程
    }
  }

  /**
   * 更新项目DSL中的页面列表
   * @private
   */
  private async updateProjectPages(file: BlockSchema, project: ProjectSchema): Promise<void> {
    try {
      const app = project.id as string;
      console.log('🔧 开始更新项目页面列表:', { fileId: file.id, projectId: app });

      // 🔧 修复：增加延迟时间，确保文件保存事务完全完成
      await new Promise(resolve => setTimeout(resolve, 800));

      // 获取当前项目DSL
      const res = await getSchema({
        app,
        name: app,
        type: SchemaType.Project
      }).catch(() => null);

      let currentProject = res?.content as ProjectSchema;
      
      // 如果项目DSL不存在，创建基础结构
      if (!currentProject || !currentProject.id) {
        console.log('📝 项目DSL不存在，创建基础结构');
        currentProject = {
          id: app,
          name: project.name || app,
          platform: project.platform || 'web',
          pages: [],
          blocks: [],
          config: {
            title: project.name || app
          }
        };
      }

      // 确保pages数组存在
      if (!currentProject.pages) {
        currentProject.pages = [];
      }

      // 检查页面是否已存在
      const pageExists = currentProject.pages.some(p => p.id === file.id);
      
      if (!pageExists) {
        // 添加页面到项目
        const pageInfo = {
          id: file.id as string,
          name: (file as any).title || (file as any).name || file.id as string,
          path: `/${file.id}`,
          type: 'page' as const,
          title: (file as any).title || (file as any).name || file.id as string
        };
        
        currentProject.pages.push(pageInfo);
        
        console.log('📝 添加页面到项目:', pageInfo);
        
        // 保存更新后的项目DSL
        await saveSchema({
          type: SchemaType.Project,
          app,
          name: app,
          content: currentProject
        });
        
        console.log('✅ 项目页面列表更新成功');
      } else {
        console.log('ℹ️ 页面已存在于项目中，无需更新');
      }
    } catch (error) {
      console.error('❌ 更新项目页面列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取文件DSL
   * @param id
   * @param project
   * @returns
   */
  async getFile(id: string, project?: ProjectSchema): Promise<BlockSchema> {
    const app = project?.id as string;
    console.log('🔧 LcdpService.getFile 获取文件:', { fileId: id, app });

    // 🔧 修复：添加重试机制，解决时序问题
    let res = null;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      res = await getSchema({
        type: SchemaType.File,
        app: app,
        name: id
      }).catch((error) => {
        console.error('❌ LcdpService.getFile 获取失败:', error);
        return null;
      });

      if (res && res.content) {
        console.log('✅ LcdpService.getFile 获取成功:', { 
          fileId: id, 
          hasContent: !!res.content,
          retryCount 
        });
        return res.content as BlockSchema;
      }

      retryCount++;
      if (retryCount < maxRetries) {
        console.log(`🔄 LcdpService.getFile 重试获取文件 (${retryCount}/${maxRetries}):`, { fileId: id });
        // 🔧 修复：增加重试间隔，给数据库更多时间
        await new Promise(resolve => setTimeout(resolve, 500 * retryCount));
      }
    }

    console.warn('⚠️ LcdpService.getFile 文件不存在或内容为空:', { fileId: id, app, retryCount });
    // 返回一个基础的空文件结构，而不是空对象
    return {
      id: id,
      componentName: 'div',
      children: [],
      __VTJ_BLOCK__: true,
      name: `页面_${id}`,
      nodes: [],
      props: [],
      state: {},
      methods: {},
      computed: {},
      watch: [],
      lifeCycles: {},
      emits: [],
      slots: [],
      css: '',
      inject: [],
      dataSources: {},
      locked: false
    } as BlockSchema;
  }

  /**
   * 获取项目中的所有文件列表
   * @param project
   * @returns
   */
  async getFiles(project?: ProjectSchema): Promise<BlockSchema[]> {
    const app = project?.id as string;
    console.log('🔧🔧🔧 LcdpService.getFiles 被调用了！获取文件列表:', { app, project });

    if (!app) {
      console.error('❌ LcdpService.getFiles 获取失败: 应用ID为空');
      return [];
    }

    try {
      // 调用后端API获取文件列表
      const res = await getSchemaList({
        type: SchemaType.File,
        app: app
      }).catch((error) => {
        console.error('❌ LcdpService.getFiles API调用失败:', error);
        return null;
      });

      if (res && Array.isArray(res)) {
        // 解析文件列表
        const files = res.map((item: any) => {
          try {
            // content字段可能是JSON字符串，需要解析
            const content = typeof item.content === 'string'
              ? JSON.parse(item.content)
              : item.content;
            return content;
          } catch (e) {
            console.warn('解析文件内容失败:', item.name, e);
            return null;
          }
        }).filter(Boolean);

        console.log('✅ LcdpService.getFiles 获取成功:', {
          app,
          count: files.length,
          fileNames: files.map(f => f.id || f.name).filter(Boolean)
        });
        return files as BlockSchema[];
      } else {
        console.log('⚠️ LcdpService.getFiles 返回数据格式异常:', res);
        return [];
      }
    } catch (error) {
      console.error('❌ LcdpService.getFiles 获取失败:', error);
      return [];
    }
  }

  /**
   * 删除文件
   * @param id
   * @param project
   * @returns
   */
  async removeFile(id: string, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    const ret = await removeSchema({
      type: SchemaType.File,
      app,
      name: id
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存历史记录
   * @param history
   * @param project
   * @returns
   */
  async saveHistory(
    history: HistorySchema,
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await saveSchema({
      type: SchemaType.History,
      app,
      name: history.id,
      content: history
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 获取历史记录
   * @param id
   * @param project
   * @returns
   */
  async getHistory(
    id: string,
    project?: ProjectSchema
  ): Promise<HistorySchema> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.History,
      app: app,
      name: id
    }).catch(() => null);

    return (res?.content || {}) as HistorySchema;
  }

  /**
   * 删除历史记录
   * @param id
   * @param project
   * @returns
   */
  async removeHistory(id: string, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.History,
      app: app,
      name: id
    }).catch(() => null);
    const content = res?.content as HistorySchema;
    if (content) {
      // 删除历史记录项
      const ids = (content.items || []).map((n: any) => n.id);
      await removeSchema({ type: SchemaType.HistoryItem, app, name: ids });
    }
    const ret = await removeSchema({
      type: SchemaType.History,
      app,
      name: id
    }).catch(() => false);

    return !!ret;
  }

  /**
   *  获取历史记录项
   * @param _fId
   * @param id
   * @param project
   * @returns
   */
  async getHistoryItem(
    _fId: string,
    id: string,
    project?: ProjectSchema
  ): Promise<HistoryItem> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.HistoryItem,
      app,
      name: id
    });
    return (res?.content || {}) as HistoryItem;
  }

  /**
   * 保存历史记录项
   * @param _fId
   * @param item
   * @param project
   * @returns
   */
  async saveHistoryItem(
    _fId: string,
    item: HistoryItem,
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await saveSchema({
      type: SchemaType.HistoryItem,
      app,
      name: item.id,
      content: item
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 删除历史记录项
   * @param _fId
   * @param ids
   * @param project
   * @returns
   */
  async removeHistoryItem(
    _fId: string,
    ids: string[],
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await removeSchema({
      type: SchemaType.HistoryItem,
      app,
      name: ids
    }).catch(() => false);
    return !!ret;
  }

  /**
   * dsl转vue代码
   * @param project
   * @param dsl
   * @returns
   */
  async genVueContent(
    project: ProjectSchema,
    dsl: BlockSchema
  ): Promise<string> {
    const app = project.id as string;
    const res = await fileGenerator({
      app,
      platform: project.platform as string,
      dsl
    }).catch(() => null);
    return res || '';
  }

  /**
   * 项目出码, 返回zip文件下载url
   * @param project
   * @returns
   */
  async genSource(project: ProjectSchema): Promise<string> {
    const app = project.id as string;
    const link = await projectGenerator({ project, app });
    return link || '';
  }

  /**
   * Vue源码转DSL
   * @param project
   * @param options
   * @returns
   */
  async parseVue(
    project: ProjectSchema,
    options: ParseVueOptions
  ): Promise<BlockSchema> {
    return await vueParser({
      project,
      ...options
    }).catch((e) => e.message);
  }

  async createRawPage(_file: PageFile): Promise<boolean> {
    this.notify('在线版本不支持源码模式页面，如需体验请使用本地版本');
    return Promise.resolve(false);
  }

  async removeRawPage(_id: string): Promise<boolean> {
    this.notify('在线演示版本不支持源码模式页面，如需体验请下载本地版本');
    return Promise.resolve(false);
  }

  async uploadStaticFile(
    _file: File,
    _rojectId: string
  ): Promise<StaticFileInfo | null> {
    this.notify('在线演示版本不支持文件上传，如需体验请下载本地版本');
    return Promise.resolve(null);
  }

  async getStaticFiles(_projectId: string): Promise<StaticFileInfo[]> {
    this.notify('在线版本不支持文件上传，如需体验请下载本地版本。');
    return Promise.resolve([]);
  }

  async getPluginMaterial(
    _from: NodeFromPlugin
  ): Promise<MaterialDescription | null> {
    this.notify('在线版本不支持引用和插件，如需体验请下载本地版本');
    return Promise.resolve(null);
  }
}
