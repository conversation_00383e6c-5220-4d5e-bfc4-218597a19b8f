package com.vtj.backend.controller;

import com.vtj.backend.dto.AppDto;
import com.vtj.backend.dto.Result;
import com.vtj.backend.service.AppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应用控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/apps")
@RequiredArgsConstructor
@Validated
@CrossOrigin(origins = {"http://localhost:*", "http://127.0.0.1:*"}, allowCredentials = "true")
public class AppController {

    private final AppService appService;

    /**
     * 创建应用
     */
    @PostMapping
    public ResponseEntity<Result<Map<String, Object>>> createApp(@Valid @RequestBody AppDto dto) {
        log.info("收到创建应用请求: {}", dto.getName() != null ? dto.getName() : dto.getAppId());

        try {
            Map<String, Object> result = appService.createApp(dto);
            return ResponseEntity.ok(Result.success(result));
        } catch (Exception e) {
            log.error("创建应用失败", e);
            throw e; // 让全局异常处理器处理
        }
    }

    /**
     * 更新应用
     */
    @PutMapping("/{appId}")
    public ResponseEntity<Result<Map<String, Object>>> updateApp(
            @PathVariable @NotBlank(message = "应用ID不能为空") String appId,
            @Valid @RequestBody AppDto dto) {
        log.info("收到更新应用请求: appId={}, label={}", appId, dto.getLabel());

        try {
            Map<String, Object> result = appService.updateApp(appId, dto);
            return ResponseEntity.ok(Result.success(result));
        } catch (Exception e) {
            log.error("更新应用失败: appId={}", appId, e);
            throw e; // 让全局异常处理器处理
        }
    }

    /**
     * 查找我的应用
     */
    @GetMapping("/action/find-my-apps")
    public ResponseEntity<Result<Map<String, Object>>> findMyApps(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit) {
        log.debug("查询用户应用列表: page={}, limit={}", page, limit);

        try {
            // 临时处理：没有用户认证系统，返回所有应用
            List<Map<String, Object>> apps = appService.findMyApps(null);

            // 包装成前端期望的分页格式
            Map<String, Object> response = new HashMap<>();
            response.put("list", apps);
            response.put("page", page);
            response.put("limit", limit);
            response.put("total", apps.size());

            return ResponseEntity.ok(Result.success(response));
        } catch (Exception e) {
            log.error("查询应用列表失败", e);
            throw e;
        }
    }

    /**
     * 根据应用ID查找应用
     */
    @GetMapping("/{appId}")
    public ResponseEntity<Result<Map<String, Object>>> findByAppId(
            @PathVariable @NotBlank(message = "应用ID不能为空") String appId) {
        log.debug("查询应用详情: appId={}", appId);
        
        try {
            Map<String, Object> app = appService.findByAppId(appId);
            if (app.isEmpty()) {
                return ResponseEntity.ok(Result.error(404, "应用不存在: " + appId));
            }
            return ResponseEntity.ok(Result.success(app));
        } catch (Exception e) {
            log.error("查询应用详情失败: appId={}", appId, e);
            throw e;
        }
    }

    /**
     * 查找所有启用的应用
     */
    @GetMapping
    public ResponseEntity<Result<List<Map<String, Object>>>> findAllActiveApps() {
        log.debug("查询所有启用的应用");

        try {
            List<Map<String, Object>> apps = appService.findAllActiveApps();
            return ResponseEntity.ok(Result.success(apps));
        } catch (Exception e) {
            log.error("查询应用列表失败", e);
            throw e;
        }
    }

    /**
     * 删除应用
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Result<Boolean>> deleteApp(
            @PathVariable @NotBlank(message = "应用ID不能为空") String id) {
        log.info("收到删除应用请求: id={}", id);

        try {
            boolean result = appService.deleteApp(id);
            if (result) {
                log.info("删除应用成功: id={}", id);
                return ResponseEntity.ok(Result.success(true));
            } else {
                log.warn("删除应用失败，应用不存在: id={}", id);
                return ResponseEntity.ok(Result.error(404, "应用不存在"));
            }
        } catch (Exception e) {
            log.error("删除应用失败: id={}", id, e);
            throw e;
        }
    }
}