package com.vtj.backend.service;

import java.util.List;
import java.util.Map;

/**
 * 系统设置服务接口
 */
public interface SettingService {

    /**
     * 获取设置
     */
    Map<String, Object> getSetting(String code);

    /**
     * 保存设置
     */
    void saveSetting(String code, String name, String value, String type, String description);

    /**
     * 获取所有设置
     */
    List<Map<String, Object>> getAllSettings();

    /**
     * 删除设置
     */
    void deleteSetting(String code);

    /**
     * 获取字典数据
     */
    List<Map<String, Object>> getDict(String code);
}
