package com.vtj.backend.controller;

import com.vtj.backend.dto.UserTopicDto;
import com.vtj.backend.dto.UserChatDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 开放接口控制器 - 处理认证等公开接口
 */
@Slf4j
@RestController
@RequestMapping("/api/open")
@RequiredArgsConstructor
public class OpenController {

    /**
     * 数据上报接口 - POST 方式
     */
    @PostMapping("/report")
    public ResponseEntity<Map<String, Object>> report(
            @RequestBody Map<String, Object> body,
            HttpServletRequest request) {
        log.info("数据上报(POST): {}", body);
        
        try {
            // 解析 base64 编码的数据
            String data = (String) body.get("data");
            if (data != null) {
                String decodedData = new String(Base64.getDecoder().decode(data));
                log.info("解码后的数据: {}", decodedData);
            }
            
            // 获取客户端IP
            String clientIp = getClientIp(request);
            log.info("客户端IP: {}", clientIp);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "上报成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("数据上报失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "上报失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 数据上报接口 - JSONP 方式
     */
    @GetMapping("/report")
    public void reportJsonp(
            @RequestParam String data,
            @RequestParam(required = false, defaultValue = "callback") String callback,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        log.info("数据上报(JSONP): data={}", data);
        
        try {
            // 解析 base64 编码的数据
            String decodedData = new String(Base64.getDecoder().decode(data));
            log.info("解码后的数据: {}", decodedData);
            
            // 获取客户端IP
            String clientIp = getClientIp(request);
            log.info("客户端IP: {}", clientIp);
            
            // 构造 JSONP 响应
            response.setContentType("application/javascript;charset=UTF-8");
            response.setHeader("Access-Control-Allow-Origin", "*");
            
            String jsonResponse = "{\"success\":true,\"message\":\"上报成功\"}";
            String jsonpResponse = callback + "(" + jsonResponse + ");";
            
            response.getWriter().write(jsonpResponse);
        } catch (Exception e) {
            log.error("数据上报失败", e);
            response.setContentType("application/javascript;charset=UTF-8");
            String jsonResponse = "{\"success\":false,\"message\":\"上报失败\"}";
            String jsonpResponse = callback + "(" + jsonResponse + ");";
            response.getWriter().write(jsonpResponse);
        }
    }

    /**
     * 获取客户端真实IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多个IP，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }

    /**
     * 登录页面接口 - 处理前端登录重定向
     */
    @GetMapping("/login")
    public void login(@RequestParam(required = false) String r, HttpServletResponse response) throws IOException {
        log.info("登录页面请求，重定向URL: {}", r);

        // 生成模拟token
        String token = "mock-token-" + System.currentTimeMillis();

        // 构建重定向URL，将token作为参数传回
        String redirectUrl = r != null ? r : "http://localhost:9529";
        String separator = redirectUrl.contains("?") ? "&" : "?";
        String finalUrl = redirectUrl + separator + "token=" + token;

        log.info("重定向到: {}", finalUrl);
        response.sendRedirect(finalUrl);
    }

    /**
     * 用户认证接口
     */
    @GetMapping("/auth/{code}")
    public ResponseEntity<Map<String, Object>> auth(@PathVariable String code) {
        log.info("用户认证请求: {}", code);

        // 模拟认证成功，返回假用户信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("token", "mock-token-123");
        response.put("user", createMockUser());

        return ResponseEntity.ok(response);
    }

    /**
     * 获取登录用户信息
     */
    @GetMapping("/user/{token}")
    public ResponseEntity<Map<String, Object>> getLoginUser(@PathVariable String token) {
        log.info("获取用户信息请求: {}", token);
        
        // 模拟返回用户信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("user", createMockUser());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取模板列表
     */
    @GetMapping("/templates")
    public ResponseEntity<Map<String, Object>> getTemplates(
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) String token) {
        log.info("获取模板列表: platform={}, token={}", platform, token);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockTemplates(platform));
        response.put("message", "获取模板列表成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取模板详情
     */
    @GetMapping("/template/{token}")
    public ResponseEntity<Map<String, Object>> getTemplate(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("获取模板详情: token={}, id={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockTemplate(id));
        response.put("message", "获取模板详情成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 删除模板
     */
    @GetMapping("/template/remove/{token}")
    public ResponseEntity<Map<String, Object>> removeTemplate(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("删除模板: token={}, id={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "模板删除成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取最新DSL
     */
    @GetMapping("/dsl/{token}")
    public ResponseEntity<Map<String, Object>> getLatestDsl(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("获取最新DSL: token={}, id={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockDsl(id));
        response.put("message", "获取DSL成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取字典数据
     */
    @GetMapping("/dict/{code}")
    public ResponseEntity<Map<String, Object>> getDict(@PathVariable String code) {
        log.info("获取字典数据: code={}", code);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockDict(code));
        response.put("message", "获取字典成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 发布模板
     */
    @PostMapping("/template/publish/{token}")
    public ResponseEntity<Map<String, Object>> publishTemplate(
            @PathVariable String token,
            @RequestParam(required = false) MultipartFile cover,
            @RequestParam String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) String category,
            @RequestParam String dsl) {
        log.info("发布模板: token={}, name={}", token, name);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("templateId", "template-" + System.currentTimeMillis());
        response.put("message", "模板发布成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 创建话题
     */
    @PostMapping("/topic/post/{token}")
    public ResponseEntity<Map<String, Object>> postTopic(
            @PathVariable String token,
            @RequestBody UserTopicDto body) {
        log.info("创建话题: token={}, title={}", token, body.getTitle());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("topicId", "topic-" + System.currentTimeMillis());
        response.put("message", "话题创建成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 上传图片创建话题
     */
    @PostMapping("/topic/image/{token}")
    public ResponseEntity<Map<String, Object>> postImageTopic(
            @PathVariable String token,
            @RequestParam MultipartFile file,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String prompt) {
        log.info("上传图片创建话题: token={}, filename={}", token, file.getOriginalFilename());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("topicId", "topic-img-" + System.currentTimeMillis());
        response.put("fileUrl", "http://example.com/uploads/" + file.getOriginalFilename());
        response.put("message", "图片话题创建成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 上传JSON创建话题
     */
    @PostMapping("/topic/json/{token}")
    public ResponseEntity<Map<String, Object>> postJsonTopic(
            @PathVariable String token,
            @RequestParam MultipartFile file,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String prompt) {
        log.info("上传JSON创建话题: token={}, filename={}", token, file.getOriginalFilename());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("topicId", "topic-json-" + System.currentTimeMillis());
        response.put("fileUrl", "http://example.com/uploads/" + file.getOriginalFilename());
        response.put("message", "JSON话题创建成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 流式对话接口
     */
    @GetMapping("/chat/stream/{token}")
    public SseEmitter streamChat(
            @PathVariable String token,
            @RequestParam String topicId,
            @RequestParam String content) {
        log.info("流式对话: token={}, topicId={}", token, topicId);

        SseEmitter emitter = new SseEmitter(30000L);

        // 模拟流式响应
        new Thread(() -> {
            try {
                String[] words = {"我", "可以", "帮助", "您", "解决", "这个", "问题"};
                for (int i = 0; i < words.length; i++) {
                    Thread.sleep(200);
                    emitter.send(SseEmitter.event()
                        .name("message")
                        .data(Map.of("content", words[i], "finished", i == words.length - 1)));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        }).start();

        return emitter;
    }

    /**
     * 上传文件
     */
    @PostMapping("/file/upload/{token}")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @PathVariable String token,
            @RequestParam MultipartFile file) {
        log.info("上传文件: token={}, filename={}", token, file.getOriginalFilename());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("fileId", "file-" + System.currentTimeMillis());
        response.put("fileUrl", "http://example.com/uploads/" + file.getOriginalFilename());
        response.put("message", "文件上传成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 删除文件
     */
    @GetMapping("/file/remove/{token}")
    public ResponseEntity<Map<String, Object>> removeFile(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("删除文件: token={}, id={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "文件删除成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取设置
     */
    @GetMapping("/setting/get/{token}")
    public ResponseEntity<Map<String, Object>> getSetting(
            @PathVariable String token,
            @RequestParam String code) {
        log.info("获取设置: token={}, code={}", token, code);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockSetting(code));
        response.put("message", "获取设置成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取系统配置信息 - 兼容前端 /open/settings/:token 接口
     */
    @GetMapping("/settings/{token}")
    public ResponseEntity<Map<String, Object>> getSettings(@PathVariable String token) {
        log.info("获取系统配置信息: token={}", token);

        // 返回系统配置信息，包含AI助手相关配置
        Map<String, Object> settings = new HashMap<>();
        settings.put("ai_model", "gpt-3.5-turbo");
        settings.put("max_tokens", 2048);
        settings.put("temperature", 0.7);
        settings.put("ai_enabled", true);
        settings.put("chat_enabled", true);
        settings.put("template_enabled", true);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", settings);
        response.put("message", "获取系统配置成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 保存设置
     */
    @PostMapping("/setting/save/{token}")
    public ResponseEntity<Map<String, Object>> saveSetting(
            @PathVariable String token,
            @RequestBody Map<String, Object> body) {
        log.info("保存设置: token={}", token);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "设置保存成功");

        return ResponseEntity.ok(response);
    }



    /**
     * 创建模拟用户数据
     */
    private Map<String, Object> createMockUser() {
        Map<String, Object> user = new HashMap<>();
        user.put("id", 1L);
        user.put("username", "demo_user");
        user.put("email", "<EMAIL>");
        user.put("avatar", "");
        user.put("status", 1);
        return user;
    }

    /**
     * 创建模拟模板列表
     */
    private java.util.List<Map<String, Object>> createMockTemplates(String platform) {
        java.util.List<Map<String, Object>> templates = new java.util.ArrayList<>();

        Map<String, Object> template1 = new HashMap<>();
        template1.put("id", "template-1");
        template1.put("name", "基础页面模板");
        template1.put("description", "包含头部、内容区域和底部的基础页面布局");
        template1.put("cover", "https://via.placeholder.com/300x200");
        template1.put("platform", platform != null ? platform : "web");
        template1.put("category", "基础模板");
        template1.put("downloads", 1250);
        templates.add(template1);

        Map<String, Object> template2 = new HashMap<>();
        template2.put("id", "template-2");
        template2.put("name", "表单页面模板");
        template2.put("description", "包含各种表单组件的页面模板");
        template2.put("cover", "https://via.placeholder.com/300x200");
        template2.put("platform", platform != null ? platform : "web");
        template2.put("category", "表单模板");
        template2.put("downloads", 890);
        templates.add(template2);

        return templates;
    }

    /**
     * 获取话题列表
     */
    @GetMapping("/topic/list/{token}")
    public ResponseEntity<Map<String, Object>> getTopics(
            @PathVariable String token,
            @RequestParam(required = false) String fileId) {
        log.info("获取话题列表: token={}, fileId={}", token, fileId);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockTopics());
        response.put("message", "获取话题列表成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取热门话题
     */
    @GetMapping("/topic/hot")
    public ResponseEntity<Map<String, Object>> getHotTopics() {
        log.info("获取热门话题");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockTopics());
        response.put("message", "获取热门话题成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 删除话题
     */
    @GetMapping("/topic/remove/{token}")
    public ResponseEntity<Map<String, Object>> removeTopic(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("删除话题: token={}, id={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "话题删除成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 获取对话列表
     */
    @GetMapping("/chat/list/{token}")
    public ResponseEntity<Map<String, Object>> getChats(
            @PathVariable String token,
            @RequestParam String id) {
        log.info("获取对话列表: token={}, topicId={}", token, id);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", createMockChats(id));
        response.put("message", "获取对话列表成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 发送对话
     */
    @PostMapping("/chat/post/{token}")
    public ResponseEntity<Map<String, Object>> postChat(
            @PathVariable String token,
            @RequestBody UserChatDto body) {
        log.info("发送对话: token={}, topicId={}", token, body.getTopicId());

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("chatId", "chat-" + System.currentTimeMillis());
        response.put("message", "对话发送成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 保存对话
     */
    @PostMapping("/chat/save/{token}")
    public ResponseEntity<Map<String, Object>> saveChat(
            @PathVariable String token,
            @RequestBody Map<String, Object> body) {
        log.info("保存对话: token={}", token);

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "对话保存成功");

        return ResponseEntity.ok(response);
    }

    /**
     * 创建模拟模板详情
     */
    private Map<String, Object> createMockTemplate(String id) {
        Map<String, Object> template = new HashMap<>();
        template.put("id", id);
        template.put("name", "模板 " + id);
        template.put("description", "这是一个示例模板");
        template.put("cover", "https://via.placeholder.com/300x200");
        template.put("platform", "web");
        template.put("category", "示例");
        template.put("downloads", 100);

        // 模拟 DSL 数据
        Map<String, Object> dsl = new HashMap<>();
        dsl.put("id", id);
        dsl.put("componentName", "Page");
        dsl.put("type", "block");
        dsl.put("props", new HashMap<>());
        dsl.put("children", new java.util.ArrayList<>());

        template.put("dsl", dsl);
        return template;
    }

    /**
     * 创建模拟DSL数据
     */
    private Map<String, Object> createMockDsl(String id) {
        Map<String, Object> dsl = new HashMap<>();
        dsl.put("id", id);
        dsl.put("version", "1.0.0");
        dsl.put("content", Map.of(
            "type", "page",
            "children", java.util.List.of(
                Map.of("type", "div", "props", Map.of("class", "container"))
            )
        ));
        dsl.put("updatedAt", System.currentTimeMillis());
        return dsl;
    }

    /**
     * 创建模拟字典数据
     */
    private java.util.List<Map<String, Object>> createMockDict(String code) {
        java.util.List<Map<String, Object>> dict = new java.util.ArrayList<>();

        if ("platform".equals(code)) {
            dict.add(Map.of("label", "Web", "value", "web"));
            dict.add(Map.of("label", "Mobile", "value", "mobile"));
            dict.add(Map.of("label", "Desktop", "value", "desktop"));
        } else if ("category".equals(code)) {
            dict.add(Map.of("label", "管理后台", "value", "admin"));
            dict.add(Map.of("label", "电商", "value", "ecommerce"));
            dict.add(Map.of("label", "企业官网", "value", "corporate"));
        }

        return dict;
    }

    /**
     * 创建模拟话题列表
     */
    private java.util.List<Map<String, Object>> createMockTopics() {
        java.util.List<Map<String, Object>> topics = new java.util.ArrayList<>();

        for (int i = 1; i <= 5; i++) {
            Map<String, Object> topic = new HashMap<>();
            topic.put("topicId", "topic-" + i);
            topic.put("title", "话题标题 " + i);
            topic.put("type", i % 2 == 0 ? "image" : "text");
            topic.put("model", "gpt-3.5-turbo");
            topic.put("createdAt", System.currentTimeMillis() - i * 86400000L);
            topics.add(topic);
        }

        return topics;
    }

    /**
     * 创建模拟对话列表
     */
    private java.util.List<Map<String, Object>> createMockChats(String topicId) {
        java.util.List<Map<String, Object>> chats = new java.util.ArrayList<>();

        // 用户消息
        Map<String, Object> userChat = new HashMap<>();
        userChat.put("chatId", "chat-user-1");
        userChat.put("topicId", topicId);
        userChat.put("role", "user");
        userChat.put("content", "请帮我设计一个登录页面");
        userChat.put("createdAt", System.currentTimeMillis() - 60000);
        chats.add(userChat);

        // AI回复
        Map<String, Object> assistantChat = new HashMap<>();
        assistantChat.put("chatId", "chat-assistant-1");
        assistantChat.put("topicId", topicId);
        assistantChat.put("role", "assistant");
        assistantChat.put("content", "我可以帮您设计一个现代化的登录页面。请告诉我您的具体需求...");
        assistantChat.put("createdAt", System.currentTimeMillis() - 30000);
        chats.add(assistantChat);

        return chats;
    }

    /**
     * 创建模拟设置数据
     */
    private Map<String, Object> createMockSetting(String code) {
        Map<String, Object> setting = new HashMap<>();
        setting.put("code", code);

        switch (code) {
            case "ai_model":
                setting.put("name", "AI模型配置");
                setting.put("value", "gpt-3.5-turbo");
                setting.put("type", "string");
                break;
            case "max_tokens":
                setting.put("name", "最大Token数");
                setting.put("value", "2048");
                setting.put("type", "number");
                break;
            case "temperature":
                setting.put("name", "温度参数");
                setting.put("value", "0.7");
                setting.put("type", "number");
                break;
            default:
                setting.put("name", "未知配置");
                setting.put("value", "");
                setting.put("type", "string");
        }

        setting.put("description", "系统配置项");
        return setting;
    }
}