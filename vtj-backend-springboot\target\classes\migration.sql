-- 数据库迁移脚本：为apps表添加platform和scope字段

-- 检查并添加platform字段
ALTER TABLE `apps` 
ADD COLUMN IF NOT EXISTS `platform` varchar(20) DEFAULT 'Web' COMMENT '平台类型: Web/H5/UniApp' AFTER `icon`;

-- 检查并添加scope字段  
ALTER TABLE `apps`
ADD COLUMN IF NOT EXISTS `scope` varchar(20) DEFAULT 'protected' COMMENT '权限范围: public/protected/private' AFTER `platform`;

-- 添加platform字段的索引
ALTER TABLE `apps` 
ADD INDEX IF NOT EXISTS `idx_platform` (`platform`);

-- 更新现有记录的默认值（如果字段为空）
UPDATE `apps` SET `platform` = 'Web' WHERE `platform` IS NULL OR `platform` = '';
UPDATE `apps` SET `scope` = 'protected' WHERE `scope` IS NULL OR `scope` = '';
