{"type": "page", "name": "123", "meta": {"title": "用户管理系统", "description": "本地同步测试页面"}, "template": "<div class=\"user-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1>� VTJ 用户管理系统 - 本地同步测试</h1>\n      <p>这是通过本地文件同步编辑的页面！支持增删改查操作</p>\n    </div>\n\n    <!-- 操作工具栏 -->\n    <div class=\"toolbar\">\n      <div class=\"search-box\">\n        <input\n          v-model=\"searchKeyword\"\n          type=\"text\"\n          placeholder=\"搜索用户名、邮箱或手机号...\"\n          class=\"search-input\"\n          @input=\"handleSearch\"\n        />\n        <button class=\"search-btn\" @click=\"handleSearch\">🔍 搜索</button>\n      </div>\n      <div class=\"action-buttons\">\n        <button class=\"btn btn-primary\" @click=\"showAddDialog\">➕ 新增用户</button>\n        <button class=\"btn btn-success\" @click=\"exportUsers\">📊 导出数据</button>\n        <button class=\"btn btn-warning\" @click=\"refreshData\">🔄 刷新</button>\n      </div>\n    </div>\n\n    <!-- 用户统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">👥</div>\n        <div class=\"stat-info\">\n          <h3>{{ totalUsers }}</h3>\n          <p>总用户数</p>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">✅</div>\n        <div class=\"stat-info\">\n          <h3>{{ activeUsers }}</h3>\n          <p>活跃用户</p>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">🚫</div>\n        <div class=\"stat-info\">\n          <h3>{{ inactiveUsers }}</h3>\n          <p>禁用用户</p>\n        </div>\n      </div>\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">🆕</div>\n        <div class=\"stat-info\">\n          <h3>{{ newUsersToday }}</h3>\n          <p>今日新增</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 用户列表表格 -->\n    <div class=\"table-container\">\n      <table class=\"user-table\">\n        <thead>\n          <tr>\n            <th>\n              <input type=\"checkbox\" v-model=\"selectAll\" @change=\"handleSelectAll\" />\n            </th>\n            <th>头像</th>\n            <th>用户名</th>\n            <th>邮箱</th>\n            <th>手机号</th>\n            <th>角色</th>\n            <th>状态</th>\n            <th>注册时间</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"user in filteredUsers\" :key=\"user.id\" :class=\"{ 'selected': selectedUsers.includes(user.id) }\">\n            <td>\n              <input type=\"checkbox\" :value=\"user.id\" v-model=\"selectedUsers\" />\n            </td>\n            <td>\n              <div class=\"avatar\">{{ user.avatar || '👤' }}</div>\n            </td>\n            <td>\n              <div class=\"user-name\">\n                <strong>{{ user.username }}</strong>\n                <small>ID: {{ user.id }}</small>\n              </div>\n            </td>\n            <td>{{ user.email }}</td>\n            <td>{{ user.phone }}</td>\n            <td>\n              <span class=\"role-tag\" :class=\"user.role\">{{ getRoleText(user.role) }}</span>\n            </td>\n            <td>\n              <span class=\"status-tag\" :class=\"user.status\">\n                {{ user.status === 'active' ? '✅ 活跃' : '🚫 禁用' }}\n              </span>\n            </td>\n            <td>{{ formatDate(user.createdAt) }}</td>\n            <td>\n              <div class=\"action-buttons\">\n                <button class=\"btn-small btn-info\" @click=\"viewUser(user)\" title=\"查看详情\">👁️</button>\n                <button class=\"btn-small btn-primary\" @click=\"editUser(user)\" title=\"编辑\">✏️</button>\n                <button class=\"btn-small btn-danger\" @click=\"deleteUser(user)\" title=\"删除\">🗑️</button>\n                <button\n                  class=\"btn-small\"\n                  :class=\"user.status === 'active' ? 'btn-warning' : 'btn-success'\"\n                  @click=\"toggleUserStatus(user)\"\n                  :title=\"user.status === 'active' ? '禁用' : '启用'\"\n                >\n                  {{ user.status === 'active' ? '🚫' : '✅' }}\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination\">\n      <button class=\"btn\" :disabled=\"currentPage === 1\" @click=\"changePage(currentPage - 1)\">上一页</button>\n      <span class=\"page-info\">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>\n      <button class=\"btn\" :disabled=\"currentPage === totalPages\" @click=\"changePage(currentPage + 1)\">下一页</button>\n    </div>\n\n    <!-- 新增/编辑用户对话框 -->\n    <div v-if=\"showDialog\" class=\"dialog-overlay\" @click=\"closeDialog\">\n      <div class=\"dialog\" @click.stop>\n        <div class=\"dialog-header\">\n          <h3>{{ isEditing ? '编辑用户' : '新增用户' }}</h3>\n          <button class=\"close-btn\" @click=\"closeDialog\">✕</button>\n        </div>\n        <div class=\"dialog-body\">\n          <form @submit.prevent=\"saveUser\">\n            <div class=\"form-group\">\n              <label>用户名 *</label>\n              <input v-model=\"currentUser.username\" type=\"text\" required />\n            </div>\n            <div class=\"form-group\">\n              <label>邮箱 *</label>\n              <input v-model=\"currentUser.email\" type=\"email\" required />\n            </div>\n            <div class=\"form-group\">\n              <label>手机号</label>\n              <input v-model=\"currentUser.phone\" type=\"tel\" />\n            </div>\n            <div class=\"form-group\">\n              <label>角色</label>\n              <select v-model=\"currentUser.role\">\n                <option value=\"admin\">管理员</option>\n                <option value=\"user\">普通用户</option>\n                <option value=\"guest\">访客</option>\n              </select>\n            </div>\n            <div class=\"form-group\">\n              <label>状态</label>\n              <select v-model=\"currentUser.status\">\n                <option value=\"active\">活跃</option>\n                <option value=\"inactive\">禁用</option>\n              </select>\n            </div>\n            <div class=\"form-actions\">\n              <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeDialog\">取消</button>\n              <button type=\"submit\" class=\"btn btn-primary\">{{ isEditing ? '更新' : '创建' }}</button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <!-- 批量操作栏 -->\n    <div v-if=\"selectedUsers.length > 0\" class=\"batch-actions\">\n      <span>已选择 {{ selectedUsers.length }} 个用户</span>\n      <button class=\"btn btn-warning\" @click=\"batchToggleStatus\">批量切换状态</button>\n      <button class=\"btn btn-danger\" @click=\"batchDelete\">批量删除</button>\n    </div>\n  </div>", "script": "", "style": "/* 页面整体样式 */\n.user-management {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n/* 页面标题 */\n.page-header {\n  text-align: center;\n  margin-bottom: 30px;\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n}\n\n.page-header h1 {\n  margin: 0 0 10px 0;\n  font-size: 2.5em;\n  font-weight: 600;\n}\n\n.page-header p {\n  margin: 0;\n  opacity: 0.9;\n  font-size: 1.1em;\n}\n\n/* 工具栏 */\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 15px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.search-box {\n  display: flex;\n  gap: 10px;\n  flex: 1;\n  max-width: 400px;\n}\n\n.search-input {\n  flex: 1;\n  padding: 10px 15px;\n  border: 2px solid #e1e5e9;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #409eff;\n}\n\n.search-btn {\n  padding: 10px 20px;\n  background: #409eff;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: background 0.3s;\n}\n\n.search-btn:hover {\n  background: #337ecc;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n/* 按钮样式 */\n.btn {\n  padding: 10px 16px;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: all 0.3s;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.btn-primary {\n  background: #409eff;\n  color: white;\n}\n\n.btn-primary:hover {\n  background: #337ecc;\n}\n\n.btn-success {\n  background: #67c23a;\n  color: white;\n}\n\n.btn-success:hover {\n  background: #5daf34;\n}\n\n.btn-warning {\n  background: #e6a23c;\n  color: white;\n}\n\n.btn-warning:hover {\n  background: #cf9236;\n}\n\n.btn-danger {\n  background: #f56c6c;\n  color: white;\n}\n\n.btn-danger:hover {\n  background: #f45454;\n}\n\n.btn-info {\n  background: #909399;\n  color: white;\n}\n\n.btn-info:hover {\n  background: #82848a;\n}\n\n.btn-secondary {\n  background: #dcdfe6;\n  color: #606266;\n}\n\n.btn-secondary:hover {\n  background: #c8c9cc;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.btn-small {\n  padding: 5px 8px;\n  font-size: 12px;\n  min-width: 30px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  transition: transform 0.3s;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n}\n\n.stat-icon {\n  font-size: 2.5em;\n  width: 60px;\n  height: 60px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n}\n\n.stat-info h3 {\n  margin: 0;\n  font-size: 2em;\n  font-weight: 600;\n  color: #303133;\n}\n\n.stat-info p {\n  margin: 5px 0 0 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 表格样式 */\n.table-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  margin-bottom: 20px;\n}\n\n.user-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.user-table th,\n.user-table td {\n  padding: 12px 15px;\n  text-align: left;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.user-table th {\n  background: #f5f7fa;\n  font-weight: 600;\n  color: #303133;\n  font-size: 14px;\n}\n\n.user-table tr:hover {\n  background: #f5f7fa;\n}\n\n.user-table tr.selected {\n  background: #ecf5ff;\n}\n\n.avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-size: 18px;\n}\n\n.user-name strong {\n  display: block;\n  color: #303133;\n  margin-bottom: 2px;\n}\n\n.user-name small {\n  color: #909399;\n  font-size: 12px;\n}\n\n.role-tag,\n.status-tag {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.role-tag.admin {\n  background: #f4f4f5;\n  color: #909399;\n}\n\n.role-tag.user {\n  background: #e1f3d8;\n  color: #67c23a;\n}\n\n.role-tag.guest {\n  background: #fdf6ec;\n  color: #e6a23c;\n}\n\n.status-tag.active {\n  background: #e1f3d8;\n  color: #67c23a;\n}\n\n.status-tag.inactive {\n  background: #fef0f0;\n  color: #f56c6c;\n}\n\n/* 分页 */\n.pagination {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 15px;\n  padding: 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.page-info {\n  color: #606266;\n  font-size: 14px;\n}\n\n/* 对话框 */\n.dialog-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.dialog {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow: auto;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n\n.dialog-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.dialog-header h3 {\n  margin: 0;\n  color: #303133;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 20px;\n  cursor: pointer;\n  color: #909399;\n  padding: 5px;\n  border-radius: 4px;\n}\n\n.close-btn:hover {\n  background: #f5f7fa;\n}\n\n.dialog-body {\n  padding: 20px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #303133;\n  font-weight: 500;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 10px 12px;\n  border: 1px solid #dcdfe6;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: border-color 0.3s;\n  box-sizing: border-box;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #409eff;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  margin-top: 30px;\n}\n\n/* 批量操作栏 */\n.batch-actions {\n  position: fixed;\n  bottom: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: white;\n  padding: 15px 20px;\n  border-radius: 8px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  z-index: 100;\n}\n\n.batch-actions span {\n  color: #606266;\n  font-weight: 500;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .user-management {\n    padding: 10px;\n  }\n\n  .toolbar {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .search-box {\n    max-width: none;\n  }\n\n  .stats-cards {\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  }\n\n  .user-table {\n    font-size: 12px;\n  }\n\n  .user-table th,\n  .user-table td {\n    padding: 8px 10px;\n  }\n\n  .dialog {\n    width: 95%;\n    margin: 10px;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.user-management > * {\n  animation: fadeIn 0.5s ease-out;\n}", "components": [], "data": {}, "methods": {}, "computed": {}, "lifecycle": {}}