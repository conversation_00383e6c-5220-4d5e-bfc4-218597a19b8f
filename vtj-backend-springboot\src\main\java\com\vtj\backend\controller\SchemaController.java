package com.vtj.backend.controller;

import com.vtj.backend.dto.Result;
import com.vtj.backend.dto.SchemaDto;
import com.vtj.backend.dto.ParseVueDto;
import com.vtj.backend.service.SchemaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Schema 控制器 - 管理 DSL 数据
 */
@Slf4j
@RestController
@RequestMapping("/api/schemas")
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:*", "http://127.0.0.1:*"}, allowCredentials = "true")
public class SchemaController {

    private final SchemaService schemaService;

    /**
     * 保存 Schema
     */
    @PostMapping("/{app}/{type}")
    public ResponseEntity<Result<Map<String, Object>>> save(
            @PathVariable String app,
            @PathVariable String type,
            @RequestBody SchemaDto dto) {
        dto.setApp(app);
        dto.setType(type);
        return ResponseEntity.ok(Result.success(schemaService.save(dto)));
    }

    /**
     * 获取单个 Schema
     */
    @GetMapping("/info/{app}/{type}")
    public ResponseEntity<Result<Map<String, Object>>> findOne(
            @PathVariable String app,
            @PathVariable String type,
            @RequestParam String name) {
        log.debug("获取Schema: app={}, type={}, name={}", app, type, name);
        Map<String, Object> result = schemaService.findOne(app, type, name);
        return ResponseEntity.ok(Result.success(result));
    }

    /**
     * 查询 Schema 列表
     */
    @GetMapping("/{app}/{type}")
    public ResponseEntity<Result<List<Map<String, Object>>>> find(
            @PathVariable String app,
            @PathVariable String type,
            @RequestParam(required = false) String name) {
        return ResponseEntity.ok(Result.success(schemaService.find(app, type, name)));
    }

    /**
     * 搜索 Schema
     */
    @GetMapping
    public ResponseEntity<List<Map<String, Object>>> search(
            @RequestParam(required = false) String app,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String keyword) {
        return ResponseEntity.ok(schemaService.search(app, type, name, keyword));
    }

    /**
     * 根据ID获取单个 Schema
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> findOneById(@PathVariable String id) {
        return ResponseEntity.ok(schemaService.findOneById(id));
    }

    /**
     * 删除 Schema
     */
    @DeleteMapping("/{app}/{type}")
    public ResponseEntity<Void> remove(
            @PathVariable String app,
            @PathVariable String type,
            @RequestBody List<String> names) {
        schemaService.remove(app, type, names);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据ID批量删除 Schema
     */
    @DeleteMapping
    public ResponseEntity<Void> removeByIds(@RequestBody List<String> ids) {
        schemaService.removeByIds(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * DSL 转 Vue 代码
     * 注意：这个接口需要调用 Node.js 服务或自行实现转换逻辑
     */
    @PostMapping("/generator/{app}/vue")
    public ResponseEntity<String> generator(
            @PathVariable String app,
            @RequestParam(defaultValue = "web") String platform,
            @RequestBody Map<String, Object> dsl) {
        return ResponseEntity.ok(schemaService.generateVue(app, platform, dsl));
    }

    /**
     * Vue 代码转 DSL
     * 注意：这个接口需要调用 Node.js 服务或自行实现转换逻辑
     */
    @PostMapping("/parser")
    public ResponseEntity<Map<String, Object>> parseVue(@RequestBody ParseVueDto dto) {
        return ResponseEntity.ok(schemaService.parseVue(dto));
    }

    /**
     * 项目出码 - 生成完整项目代码包
     */
    @PostMapping("/generator/{app}/project")
    public ResponseEntity<Map<String, Object>> generateProject(
            @PathVariable String app,
            @RequestBody Map<String, Object> project) {
        return ResponseEntity.ok(schemaService.generateProject(app, project));
    }
}