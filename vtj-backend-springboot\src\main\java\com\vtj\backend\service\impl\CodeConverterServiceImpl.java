package com.vtj.backend.service.impl;

import com.alibaba.fastjson.JSON;
import com.vtj.backend.dto.ParseVueDto;
import com.vtj.backend.service.CodeConverterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 代码转换服务实现类
 * 通过 HTTP 调用 Node.js 微服务进行代码转换
 */
@Slf4j
@Service
public class CodeConverterServiceImpl implements CodeConverterService {

    @Value("${vtj.coder.service.url:http://localhost:3001}")
    private String coderServiceUrl;

    private final RestTemplate restTemplate;

    public CodeConverterServiceImpl() {
        this.restTemplate = new RestTemplate();
    }

    @Override
    public String generateVue(String app, String platform, Map<String, Object> dsl,
                             Map<String, Object> componentMap, Object dependencies) {
        try {
            String url = coderServiceUrl + "/api/generator/vue";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dsl", dsl);
            requestBody.put("platform", platform);
            requestBody.put("formatterDisabled", false);
            
            if (componentMap != null) {
                requestBody.put("componentMap", componentMap);
            }
            if (dependencies != null) {
                requestBody.put("dependencies", dependencies);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            log.info("调用代码转换服务 - DSL转Vue: {}", url);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                Boolean success = (Boolean) result.get("success");
                
                if (Boolean.TRUE.equals(success)) {
                    String code = (String) result.get("code");
                    log.info("DSL转Vue成功，代码长度: {}", code != null ? code.length() : 0);
                    return code;
                } else {
                    String error = (String) result.get("error");
                    log.error("DSL转Vue失败: {}", error);
                    throw new RuntimeException("DSL转Vue失败: " + error);
                }
            } else {
                throw new RuntimeException("代码转换服务响应异常");
            }
            
        } catch (Exception e) {
            log.error("调用代码转换服务失败", e);
            // 返回默认模板，避免完全失败
            return generateDefaultVueTemplate(dsl);
        }
    }

    @Override
    public Map<String, Object> parseVue(ParseVueDto dto) {
        try {
            String url = coderServiceUrl + "/api/parser/vue";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("id", dto.getId());
            requestBody.put("name", dto.getName());
            requestBody.put("source", dto.getSource());
            
            if (dto.getProject() != null) {
                // 如果 project 是字符串，需要解析为对象
                if (dto.getProject() instanceof String) {
                    requestBody.put("project", JSON.parseObject((String) dto.getProject()));
                } else {
                    requestBody.put("project", dto.getProject());
                }
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            log.info("调用代码转换服务 - Vue转DSL: {}", url);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                Boolean success = (Boolean) result.get("success");
                
                if (Boolean.TRUE.equals(success)) {
                    Map<String, Object> dsl = (Map<String, Object>) result.get("dsl");
                    log.info("Vue转DSL成功");
                    return dsl;
                } else {
                    String error = (String) result.get("error");
                    log.error("Vue转DSL失败: {}", error);
                    throw new RuntimeException("Vue转DSL失败: " + error);
                }
            } else {
                throw new RuntimeException("代码转换服务响应异常");
            }
            
        } catch (Exception e) {
            log.error("调用代码转换服务失败", e);
            // 返回默认DSL，避免完全失败
            return generateDefaultDsl(dto);
        }
    }

    @Override
    public boolean isServiceAvailable() {
        try {
            String url = coderServiceUrl + "/health";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.warn("代码转换服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成默认Vue模板（当转换服务不可用时）
     */
    private String generateDefaultVueTemplate(Map<String, Object> dsl) {
        String componentName = "GeneratedComponent";
        if (dsl != null && dsl.containsKey("componentName")) {
            componentName = (String) dsl.get("componentName");
        }
        
        return String.format("""
            <template>
              <div class="generated-component">
                <h2>%s</h2>
                <p>代码转换服务暂时不可用，这是默认模板</p>
                <!-- DSL: %s -->
              </div>
            </template>
            
            <script setup>
            // Generated by VTJ Platform
            // Component: %s
            </script>
            
            <style scoped>
            .generated-component {
              padding: 20px;
              border: 1px dashed #ccc;
              text-align: center;
            }
            </style>
            """, componentName, JSON.toJSONString(dsl), componentName);
    }

    /**
     * 生成默认DSL（当转换服务不可用时）
     */
    private Map<String, Object> generateDefaultDsl(ParseVueDto dto) {
        Map<String, Object> dsl = new HashMap<>();
        dsl.put("id", dto.getId());
        dsl.put("componentName", dto.getName());
        dsl.put("type", "block");
        dsl.put("props", new HashMap<>());
        dsl.put("children", new java.util.ArrayList<>());
        dsl.put("meta", Map.of(
            "generated", true,
            "reason", "代码转换服务不可用",
            "timestamp", System.currentTimeMillis()
        ));
        return dsl;
    }
}
