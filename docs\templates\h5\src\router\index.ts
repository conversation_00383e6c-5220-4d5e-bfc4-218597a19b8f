import { createRouter, createWebHashHistory } from 'vue-router';
import AppContainer from '@/components/AppContainer.vue';
import { PROJECT_ID, ROUTER_APPEND_TO } from '@/contants';
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: `/${PROJECT_ID}`
    },
    {
      path: '/:app',
      name: ROUTER_APPEND_TO,
      component: AppContainer,
      children: []
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/not-found.vue')
    }
  ]
});

export default router;
