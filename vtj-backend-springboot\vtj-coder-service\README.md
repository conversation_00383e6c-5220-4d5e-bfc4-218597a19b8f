# VTJ 代码转换微服务

这是一个独立的 Node.js 微服务，专门处理 VTJ 低代码平台的代码转换功能。

## 功能特性

- 🔄 **DSL 转 Vue**：将低代码 DSL 转换为 Vue 组件代码
- 🔄 **Vue 转 DSL**：将 Vue 组件代码解析为低代码 DSL
- 🚀 **高性能**：基于 Express.js 构建，支持高并发
- 🛡️ **安全性**：集成 Helmet 安全中间件
- 📦 **压缩**：自动压缩响应数据

## 快速开始

### 1. 安装依赖

```bash
cd vtj-coder-service
npm install
```

### 2. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 3. 验证服务

访问健康检查接口：
```bash
curl http://localhost:3001/health
```

## API 接口

### DSL 转 Vue

**POST** `/api/generator/vue`

请求体：
```json
{
  "dsl": {}, 
  "componentMap": {},
  "dependencies": [],
  "platform": "web",
  "formatterDisabled": false
}
```

响应：
```json
{
  "success": true,
  "code": "<template>...</template>",
  "message": "DSL 转 Vue 成功"
}
```

### Vue 转 DSL

**POST** `/api/parser/vue`

请求体：
```json
{
  "id": "page-id",
  "name": "PageName", 
  "source": "<template>...</template>",
  "project": {}
}
```

响应：
```json
{
  "success": true,
  "dsl": {},
  "message": "Vue 转 DSL 成功"
}
```

## 环境变量

- `PORT`: 服务端口，默认 3001

## 部署说明

1. 确保 Node.js 版本 >= 16
2. 安装 PM2 进行进程管理
3. 配置反向代理（Nginx）
4. 设置环境变量和日志

## 与 Spring Boot 集成

Spring Boot 后端通过 HTTP 调用此微服务：

```java
// 调用代码转换服务
RestTemplate restTemplate = new RestTemplate();
String url = "http://localhost:3001/api/generator/vue";
// ... 发送请求
```
