package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Schema 实体类 - 存储 DSL 数据
 */
@Data
@Entity
@Table(name = "`schemas`",
    uniqueConstraints = @UniqueConstraint(columnNames = {"app", "type", "name"}))
@EqualsAndHashCode(callSuper = true)
public class Schema extends BaseEntity {

    /**
     * 所属应用
     */
    @Column(nullable = false, length = 50)
    private String app;

    /**
     * 类型: page/component/material/project
     */
    @Column(nullable = false, length = 20)
    private String type;

    /**
     * Schema名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * DSL内容(JSON)
     */
    @Lob
    @Column(columnDefinition = "LONGTEXT")
    private String content;

    /**
     * 版本号
     */
    @Column(columnDefinition = "INT DEFAULT 1")
    private Integer version = 1;

    /**
     * 描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 创建用户ID
     */
    @Column(name = "user_id")
    private Long userId;
} 