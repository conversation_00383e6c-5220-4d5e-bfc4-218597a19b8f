#!/bin/bash

echo "========================================"
echo "    启动 VTJ 低代码平台后端服务"
echo "========================================"

echo ""
echo "[1/3] 检查环境..."

# 检查 Java
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安装或未配置环境变量"
    exit 1
fi
echo "✅ Java 环境正常"

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装或未配置环境变量"
    exit 1
fi
echo "✅ Node.js 环境正常"

# 检查 MySQL
echo "⚠️  请确保 MySQL 服务已启动，数据库 vtj_db 已创建"

echo ""
echo "[2/3] 启动代码转换服务..."
cd vtj-coder-service

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装 Node.js 依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 启动 Node.js 服务
echo "🚀 启动代码转换服务 (端口: 3001)..."
npm start &
CODER_PID=$!

# 等待服务启动
sleep 3

echo ""
echo "[3/3] 启动 Spring Boot 服务..."
cd ..

# 启动 Spring Boot 服务
echo "🚀 启动 Spring Boot 服务 (端口: 8080)..."
mvn spring-boot:run &
SPRING_PID=$!

echo ""
echo "========================================"
echo "    服务启动完成！"
echo "========================================"
echo ""
echo "📍 代码转换服务: http://localhost:3001"
echo "📍 Spring Boot API: http://localhost:8080/api"
echo "📍 健康检查: http://localhost:3001/health"
echo ""
echo "💡 提示："
echo "   - 按 Ctrl+C 停止所有服务"
echo "   - 确保 MySQL 服务正在运行"
echo ""

# 等待用户中断
trap "echo ''; echo '停止服务...'; kill $CODER_PID $SPRING_PID 2>/dev/null; exit 0" INT

# 保持脚本运行
wait
