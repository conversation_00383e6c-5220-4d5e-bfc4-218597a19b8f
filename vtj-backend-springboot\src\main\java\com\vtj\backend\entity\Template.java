package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "templates")
public class Template extends BaseEntity {

    /**
     * 模板ID
     */
    @Column(name = "template_id", nullable = false, unique = true, length = 50)
    private String templateId;

    /**
     * 模板名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 模板描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 封面图
     */
    @Column(name = "cover", length = 500)
    private String cover;

    /**
     * 平台类型
     */
    @Column(name = "platform", length = 20)
    private String platform = "web";

    /**
     * 分类
     */
    @Column(name = "category", length = 50)
    private String category;

    /**
     * 标签(逗号分隔)
     */
    @Column(name = "tags", length = 200)
    private String tags;

    /**
     * 状态: 0-草稿 1-发布
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 浏览次数
     */
    @Column(name = "views")
    private Integer views = 0;

    /**
     * 下载次数
     */
    @Column(name = "downloads")
    private Integer downloads = 0;

    /**
     * 创建用户ID
     */
    @Column(name = "user_id")
    private Long userId;
}
