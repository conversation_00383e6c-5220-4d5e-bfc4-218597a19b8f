package com.vtj.backend.service;

import java.util.List;
import java.util.Map;

/**
 * 模板服务接口
 */
public interface TemplateService {

    /**
     * 获取模板列表
     */
    List<Map<String, Object>> getTemplates(String platform, String category, String keyword);

    /**
     * 获取模板详情
     */
    Map<String, Object> getTemplate(String templateId);

    /**
     * 发布模板
     */
    String publishTemplate(String name, String description, String platform, 
                          String category, String dsl, String coverUrl);

    /**
     * 删除模板
     */
    void removeTemplate(String templateId);

    /**
     * 获取模板DSL
     */
    Map<String, Object> getTemplateDsl(String templateId);

    /**
     * 保存模板DSL版本
     */
    void saveTemplateDsl(String templateId, String version, String dsl, String description);

    /**
     * 增加下载次数
     */
    void incrementDownloads(String templateId);
}
