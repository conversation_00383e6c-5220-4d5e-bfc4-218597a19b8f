package com.vtj.backend.service;

import com.vtj.backend.dto.AppDto;

import java.util.List;
import java.util.Map;

/**
 * 应用服务接口
 */
public interface AppService {

    /**
     * 创建应用
     */
    Map<String, Object> createApp(AppDto dto);

    /**
     * 更新应用
     */
    Map<String, Object> updateApp(String appId, AppDto dto);

    /**
     * 查找我的应用
     */
    List<Map<String, Object>> findMyApps(Long userId);

    /**
     * 根据应用ID查找应用
     */
    Map<String, Object> findByAppId(String appId);

    /**
     * 查找所有启用的应用
     */
    List<Map<String, Object>> findAllActiveApps();

    /**
     * 删除应用
     */
    boolean deleteApp(String id);
}