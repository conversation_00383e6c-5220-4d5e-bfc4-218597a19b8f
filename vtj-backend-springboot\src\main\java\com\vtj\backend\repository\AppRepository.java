package com.vtj.backend.repository;

import com.vtj.backend.entity.App;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 应用数据访问层
 */
@Repository
public interface AppRepository extends JpaRepository<App, Long> {

    /**
     * 根据应用ID查找
     */
    Optional<App> findByAppId(String appId);

    /**
     * 根据用户ID查找应用列表
     */
    List<App> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 查找所有启用的应用
     */
    List<App> findByStatusOrderByCreatedAtDesc(Integer status);
} 