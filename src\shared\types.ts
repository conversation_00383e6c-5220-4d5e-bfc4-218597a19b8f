export type PlatformTypeVO = 'Web' | 'H5' | 'UniApp';

export type AppScope = 'public' | 'private' | 'protected';

export interface ApiListResponse<T = any> {
  list: T[];
  page: number;
  limit: number;
  total: number;
}

export interface LowCodeAppVO {
  id?: string;
  name: string;
  label: string;
  platform: PlatformTypeVO;
  scope: AppScope;
  userId?: string;
}

export enum SchemaType {
  Project = 'project',
  Material = 'material',
  File = 'page',  // 修复：页面类型应该是'page'而不是'file'
  History = 'history',
  HistoryItem = 'history-item'
}
