# VTJ 本地同步工具 🚀

一个强大的工具，实现VTJ低代码平台与本地Vue文件的双向同步，让你享受本地IDE开发的便利！

## ✨ 功能特性

- 🔄 **双向同步** - 本地Vue文件 ↔ VTJ平台DSL
- 👀 **实时监听** - 文件变化自动同步到平台
- 📥 **批量拉取** - 一键拉取所有平台页面到本地
- 📤 **批量推送** - 一键推送所有本地文件到平台
- 🛡️ **安全备份** - 自动备份被覆盖的文件
- 🎨 **友好界面** - 彩色输出和进度提示
- ⚙️ **灵活配置** - 支持多种配置选项

## 🚀 快速开始

### 1. 安装依赖

```bash
cd vtj-sync-tool
npm install
```

### 2. 初始化配置

```bash
# 交互式配置
npm run init

# 或者使用CLI
node cli.js init
```

### 3. 开始使用

```bash
# 拉取所有页面到本地
npm run pull

# 开始监听文件变化
npm run watch

# 查看状态
node cli.js status
```

## 📋 命令说明

### 基础命令

```bash
# 初始化配置
vtj-sync init

# 拉取页面
vtj-sync pull [页面名]     # 拉取指定页面
vtj-sync pull --all       # 拉取所有页面

# 推送页面
vtj-sync push [页面名]     # 推送指定页面
vtj-sync push --all       # 推送所有页面

# 监听模式
vtj-sync watch            # 开始监听文件变化

# 状态信息
vtj-sync status           # 显示同步状态

# 交互式菜单
vtj-sync menu             # 显示操作菜单
```

### NPM Scripts

```bash
npm run start    # 启动同步工具
npm run watch    # 监听模式
npm run pull     # 拉取所有页面
npm run push     # 推送所有页面
npm run init     # 初始化配置
```

## ⚙️ 配置文件

`vtj-config.json` 配置说明：

```json
{
  "apiBase": "http://localhost:8080/api",  // VTJ API地址
  "app": "your-app-name",                  // 应用名称
  "localDir": "./src/pages",               // 本地目录
  "platform": "web",                       // 目标平台
  "autoSync": true,                        // 自动同步
  "watchMode": true,                       // 监听模式
  "syncInterval": 1000,                    // 同步间隔(ms)
  "backupEnabled": true,                   // 启用备份
  "backupDir": "./backup",                 // 备份目录
  "excludeFiles": [                        // 排除文件
    "*.backup",
    "*.tmp",
    "node_modules/**"
  ],
  "logLevel": "info"                       // 日志级别
}
```

## 🔄 工作流程

### 开发流程

```mermaid
graph LR
    A[VTJ平台创建页面] --> B[拉取到本地]
    B --> C[本地IDE编辑]
    C --> D[自动同步到平台]
    D --> E[平台实时预览]
```

### 具体步骤

1. **在VTJ平台创建页面**
   - 使用拖拽方式快速搭建基础结构
   - 设置基本属性和布局

2. **拉取到本地开发**
   ```bash
   vtj-sync pull HomePage
   ```

3. **本地IDE编辑**
   ```vue
   <!-- src/pages/HomePage.vue -->
   <template>
     <div class="home-page">
       <h1>{{ title }}</h1>
       <el-button @click="handleClick">点击我</el-button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'

   const title = ref('欢迎使用VTJ')

   const handleClick = () => {
     console.log('按钮被点击了!')
   }
   </script>

   <style scoped>
   .home-page {
     padding: 20px;
     text-align: center;
   }
   </style>
   ```

4. **自动同步到平台**
   - 保存文件后自动推送到VTJ平台
   - 在平台上实时查看效果

## 🛠️ 高级用法

### 批量操作

```bash
# 拉取所有页面
vtj-sync pull --all

# 推送所有页面
vtj-sync push --all
```

### 监听模式

```bash
# 启动监听
vtj-sync watch

# 监听会自动处理：
# - 文件修改 → 自动推送
# - 新增文件 → 自动推送
# - 删除文件 → 提示信息
```

### 状态查看

```bash
vtj-sync status
```

输出示例：
```
📊 VTJ同步工具状态
────────────────────────────────────────
配置信息:
  应用名称: asdfasdf
  API地址: http://localhost:8080/api
  本地目录: ./src/pages
  平台: web

本地文件: 3 个
  📄 HomePage.vue
  📄 AboutPage.vue
  📄 ContactPage.vue

平台页面: 3 个
  🌐 HomePage
  🌐 AboutPage
  🌐 ContactPage
```

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   ```bash
   # 检查VTJ后端是否启动
   curl http://localhost:8080/api/open/auth/test
   
   # 检查配置文件中的apiBase是否正确
   ```

2. **页面拉取失败**
   ```bash
   # 确认应用名称是否正确
   # 确认页面在平台上是否存在
   vtj-sync status  # 查看平台页面列表
   ```

3. **文件监听不工作**
   ```bash
   # 确认本地目录是否存在
   # 确认文件权限是否正确
   # 重启监听服务
   ```

### 调试模式

修改配置文件中的 `logLevel` 为 `debug` 获取详细日志：

```json
{
  "logLevel": "debug"
}
```

## 📝 注意事项

1. **备份重要文件** - 工具会自动备份，但建议使用Git管理代码
2. **网络连接** - 确保能访问VTJ后端API
3. **文件格式** - 只同步 `.vue` 文件
4. **命名规范** - 页面名称应符合Vue组件命名规范

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
