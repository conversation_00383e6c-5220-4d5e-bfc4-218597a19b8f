<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTJ 低代码平台工作流测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .code-display {
            background: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', monospace;
        }
    </style>
</head>
<body>
    <h1>🚀 VTJ 低代码平台工作流测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p>此测试将验证 Spring Boot 后端是否正确实现了低代码设计器的核心功能：</p>
        <ul>
            <li>✅ 创建应用</li>
            <li>✅ 保存项目 DSL</li>
            <li>✅ 保存页面 DSL</li>
            <li>✅ DSL 转 Vue 代码</li>
            <li>✅ 数据持久化验证</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试控制</h2>
        <button onclick="runCompleteWorkflow()">🎯 运行完整工作流测试</button>
        <button onclick="clearResults()">🧹 清空结果</button>
    </div>

    <div id="test-results"></div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        const TEST_APP_ID = `test-app-${Date.now()}`;
        let testResults = [];

        function addResult(step, success, message, data) {
            const resultDiv = document.getElementById('test-results');
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step';
            
            stepDiv.innerHTML = `
                <div class="step-title ${success ? 'success' : 'error'}">
                    ${success ? '✅' : '❌'} 步骤 ${step}: ${message}
                </div>
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
            
            resultDiv.appendChild(stepDiv);
            testResults.push({ step, success, message, data });
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testResults = [];
        }

        async function runCompleteWorkflow() {
            clearResults();
            
            // 步骤 1: 创建应用
            try {
                const appResponse = await fetch(`${API_BASE}/apps`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: TEST_APP_ID,
                        label: 'VTJ 测试应用',
                        platform: 'web'
                    })
                });
                const appData = await appResponse.json();
                addResult(1, appResponse.ok, '创建应用', appData);
                
                if (!appResponse.ok) return;
            } catch (error) {
                addResult(1, false, '创建应用失败', { error: error.message });
                return;
            }

            // 步骤 2: 保存项目 DSL
            const projectDsl = {
                id: TEST_APP_ID,
                name: 'VTJ 测试项目',
                platform: 'web',
                version: '1.0.0',
                description: '这是一个测试项目',
                pages: [
                    { id: 'home', title: '首页', homepage: true }
                ],
                blocks: [],
                config: {
                    title: 'VTJ 测试应用'
                }
            };

            try {
                const projectResponse = await fetch(`${API_BASE}/schemas/${TEST_APP_ID}/project`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        app: TEST_APP_ID,
                        type: 'project',
                        name: TEST_APP_ID,
                        content: JSON.stringify(projectDsl),
                        description: '项目配置'
                    })
                });
                const projectData = await projectResponse.json();
                addResult(2, projectResponse.ok, '保存项目 DSL', projectData);
                
                if (!projectResponse.ok) return;
            } catch (error) {
                addResult(2, false, '保存项目 DSL 失败', { error: error.message });
                return;
            }

            // 步骤 3: 保存页面 DSL
            const pageDsl = {
                id: 'home',
                componentName: 'Page',
                type: 'page',
                title: '首页',
                props: {
                    style: {
                        padding: '20px'
                    }
                },
                children: [
                    {
                        componentName: 'div',
                        props: {
                            style: {
                                textAlign: 'center',
                                marginBottom: '20px'
                            }
                        },
                        children: [
                            {
                                componentName: 'h1',
                                children: ['欢迎使用 VTJ 低代码平台']
                            },
                            {
                                componentName: 'p',
                                children: ['这是通过 DSL 生成的页面']
                            }
                        ]
                    },
                    {
                        componentName: 'ElButton',
                        props: {
                            type: 'primary',
                            onClick: '() => { alert("Hello VTJ!") }'
                        },
                        children: ['点击我']
                    }
                ]
            };

            try {
                const pageResponse = await fetch(`${API_BASE}/schemas/${TEST_APP_ID}/file`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        app: TEST_APP_ID,
                        type: 'file',
                        name: 'home',
                        content: JSON.stringify(pageDsl),
                        description: '首页'
                    })
                });
                const pageData = await pageResponse.json();
                addResult(3, pageResponse.ok, '保存页面 DSL', pageData);
                
                if (!pageResponse.ok) return;
            } catch (error) {
                addResult(3, false, '保存页面 DSL 失败', { error: error.message });
                return;
            }

            // 步骤 4: DSL 转 Vue 代码
            try {
                const vueResponse = await fetch(`${API_BASE}/schemas/generator/${TEST_APP_ID}/vue?platform=web`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(pageDsl)
                });
                
                let vueCode = '';
                if (vueResponse.ok) {
                    vueCode = await vueResponse.text();
                    
                    // 创建一个特殊的显示区域来展示生成的 Vue 代码
                    const codeSection = document.createElement('div');
                    codeSection.className = 'test-section';
                    codeSection.innerHTML = `
                        <h3>生成的 Vue 代码：</h3>
                        <div class="code-display">${escapeHtml(vueCode)}</div>
                    `;
                    document.getElementById('test-results').appendChild(codeSection);
                    
                    addResult(4, true, 'DSL 转 Vue 代码成功', { length: vueCode.length + ' 字符' });
                } else {
                    const errorData = await vueResponse.json();
                    addResult(4, false, 'DSL 转 Vue 代码失败', errorData);
                }
            } catch (error) {
                addResult(4, false, 'DSL 转 Vue 代码失败', { error: error.message });
            }

            // 步骤 5: 验证数据持久化
            try {
                // 验证项目 DSL
                const getProjectResponse = await fetch(`${API_BASE}/schemas/info/${TEST_APP_ID}/project?name=${TEST_APP_ID}`);
                const savedProject = await getProjectResponse.json();
                
                // 验证页面 DSL
                const getPageResponse = await fetch(`${API_BASE}/schemas/info/${TEST_APP_ID}/file?name=home`);
                const savedPage = await getPageResponse.json();
                
                const verificationSuccess = getProjectResponse.ok && getPageResponse.ok &&
                    savedProject.content && savedPage.content;
                
                addResult(5, verificationSuccess, '数据持久化验证', {
                    projectSaved: !!savedProject.content,
                    pageSaved: !!savedPage.content,
                    projectId: savedProject.id,
                    pageId: savedPage.id
                });
            } catch (error) {
                addResult(5, false, '数据持久化验证失败', { error: error.message });
            }

            // 测试总结
            const successCount = testResults.filter(r => r.success).length;
            const totalCount = testResults.length;
            
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-section';
            summaryDiv.innerHTML = `
                <h2>测试总结</h2>
                <p class="${successCount === totalCount ? 'success' : 'error'}">
                    测试完成：${successCount}/${totalCount} 成功
                </p>
                ${successCount === totalCount ? 
                    '<p class="success">✅ Spring Boot 后端已正确实现低代码设计器的核心功能！</p>' :
                    '<p class="error">❌ 部分功能未正确实现，请检查错误信息。</p>'}
            `;
            document.getElementById('test-results').appendChild(summaryDiv);
        }

        function escapeHtml(html) {
            const div = document.createElement('div');
            div.textContent = html;
            return div.innerHTML;
        }
    </script>
</body>
</html> 