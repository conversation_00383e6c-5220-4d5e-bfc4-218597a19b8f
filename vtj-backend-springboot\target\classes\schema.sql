-- 创建数据库
CREATE DATABASE IF NOT EXISTS vtj_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE vtj_db;

-- 系统配置表
CREATE TABLE IF NOT EXISTS `settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '配置代码',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `value` text COMMENT '配置值',
  `type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(200) COMMENT '配置描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 应用表
CREATE TABLE IF NOT EXISTS `apps` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app_id` varchar(50) NOT NULL COMMENT '应用ID',
  `name` varchar(100) NOT NULL COMMENT '应用名称',
  `description` text COMMENT '应用描述',
  `icon` varchar(500) COMMENT '应用图标',
  `platform` varchar(20) DEFAULT 'Web' COMMENT '平台类型: Web/H5/UniApp',
  `scope` varchar(20) DEFAULT 'protected' COMMENT '权限范围: public/protected/private',
  `status` tinyint DEFAULT 1 COMMENT '状态: 0-禁用 1-启用',
  `user_id` bigint COMMENT '创建用户ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id` (`app_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用表';

-- Schema表 (存储DSL数据)
CREATE TABLE IF NOT EXISTS `schemas` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(50) NOT NULL COMMENT '所属应用',
  `type` varchar(20) NOT NULL COMMENT '类型: page/component/material/project',
  `name` varchar(100) NOT NULL COMMENT 'Schema名称',
  `content` longtext COMMENT 'DSL内容(JSON)',
  `version` int DEFAULT 1 COMMENT '版本号',
  `description` text COMMENT '描述',
  `user_id` bigint COMMENT '创建用户ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_type_name` (`app`, `type`, `name`),
  KEY `idx_app` (`app`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Schema存储表';

-- 模板信息表
CREATE TABLE IF NOT EXISTS `templates` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `template_id` varchar(50) NOT NULL COMMENT '模板ID',
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `cover` varchar(500) COMMENT '封面图',
  `platform` varchar(20) DEFAULT 'web' COMMENT '平台: web/h5/uniapp',
  `category` varchar(50) COMMENT '分类',
  `tags` varchar(200) COMMENT '标签(逗号分隔)',
  `status` tinyint DEFAULT 1 COMMENT '状态: 0-草稿 1-发布',
  `views` int DEFAULT 0 COMMENT '浏览次数',
  `downloads` int DEFAULT 0 COMMENT '下载次数',
  `user_id` bigint COMMENT '创建用户ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_id` (`template_id`),
  KEY `idx_platform` (`platform`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板信息表';

-- 模板DSL版本记录表
CREATE TABLE IF NOT EXISTS `template_dsl` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `template_id` varchar(50) NOT NULL COMMENT '模板ID',
  `version` varchar(20) NOT NULL COMMENT '版本号',
  `dsl` longtext NOT NULL COMMENT 'DSL内容',
  `description` text COMMENT '版本描述',
  `is_current` tinyint DEFAULT 0 COMMENT '是否当前版本',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_version` (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板DSL版本记录表';

-- AI话题表
CREATE TABLE IF NOT EXISTS `topics` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `topic_id` varchar(50) NOT NULL COMMENT '话题ID',
  `title` varchar(200) NOT NULL COMMENT '话题标题',
  `type` varchar(20) DEFAULT 'text' COMMENT '类型: text/image/json',
  `model` varchar(50) COMMENT 'AI模型',
  `file_url` varchar(500) COMMENT '文件URL(图片/JSON)',
  `prompt` text COMMENT '提示词',
  `status` tinyint DEFAULT 1 COMMENT '状态',
  `user_id` bigint COMMENT '用户ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_topic_id` (`topic_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI话题表';

-- AI对话记录表
CREATE TABLE IF NOT EXISTS `chats` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `chat_id` varchar(50) NOT NULL COMMENT '对话ID',
  `topic_id` varchar(50) NOT NULL COMMENT '话题ID',
  `role` varchar(20) NOT NULL COMMENT '角色: user/assistant',
  `content` text NOT NULL COMMENT '对话内容',
  `tokens` int DEFAULT 0 COMMENT 'Token消耗',
  `status` tinyint DEFAULT 1 COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_chat_id` (`chat_id`),
  KEY `idx_topic_id` (`topic_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话记录表';

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) COMMENT '邮箱',
  `phone` varchar(20) COMMENT '手机号',
  `avatar` varchar(500) COMMENT '头像',
  `status` tinyint DEFAULT 1 COMMENT '状态: 0-禁用 1-启用',
  `last_login_at` datetime COMMENT '最后登录时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入默认数据
INSERT INTO `settings` (`code`, `name`, `value`, `type`, `description`) VALUES
('system.title', '系统标题', 'VTJ低代码平台', 'string', '系统显示标题'),
('ai.model', 'AI模型', 'gpt-3.5-turbo', 'string', '默认AI模型'),
('ai.api_key', 'AI API Key', '', 'string', 'OpenAI API密钥'),
('oss.enabled', 'OSS启用', 'true', 'boolean', '是否启用OSS存储');

-- 插入测试用户
INSERT INTO `users` (`username`, `password`, `email`) VALUES
('admin', '$2a$10$EixKPVBpI.gRqCmLKFhRsOXFKxK6VaLYmG8jU7/Qs6j/7xCZRQGKG', '<EMAIL>'); -- 密码: admin123 