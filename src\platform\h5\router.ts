import { createRouter, createWebHashHistory } from 'vue-router';
import Page from './Page.vue';
import NotFound from './NotFound.vue';
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: () => {
        // 从URL hash中提取应用ID，如果没有则显示404
        const hash = location.hash;
        const match = hash.match(/#\/([^\/]+)/);
        if (match && match[1]) {
          return `/${match[1]}`;
        }
        return '/404';
      }
    },
    {
      path: '/:app',
      component: Page
    },
    {
      path: '/:app/page/:id',
      component: Page
    },
    {
      path: '/404',
      component: NotFound
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: NotFound
    }
  ]
});

export default router;
