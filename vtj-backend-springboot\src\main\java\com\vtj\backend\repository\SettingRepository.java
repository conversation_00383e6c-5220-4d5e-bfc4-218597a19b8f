package com.vtj.backend.repository;

import com.vtj.backend.entity.Setting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 系统配置数据访问层
 */
@Repository
public interface SettingRepository extends JpaRepository<Setting, Long> {

    /**
     * 根据配置代码查找
     */
    Optional<Setting> findByCode(String code);

    /**
     * 根据配置代码删除
     */
    void deleteByCode(String code);
}
