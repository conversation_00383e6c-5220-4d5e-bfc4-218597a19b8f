/*!
 * pinia v2.1.7
 * (c) 2023 <PERSON>
 * @license MIT
 */
var Pinia = (function (exports) {
  'use strict';

  // 简化的 Vue Demi 兼容层
  const vueDemi = {
    hasInjectionContext: () => false,
    inject: () => null,
    effectScope: (detached) => ({
      run: (fn) => fn(),
      stop: () => {}
    }),
    ref: (value) => ({ value }),
    markRaw: (obj) => obj,
    isVue2: false
  };

  /**
   * setActivePinia must be called to handle SSR at the top of functions like
   * `fetch`, `setup`, `serverPrefetch` and others
   */
  let activePinia;
  /**
   * Sets or unsets the active pinia. Used in SSR and internally when calling
   * actions and getters
   *
   * @param pinia - Pinia instance
   */
  // @ts-expect-error: cannot constrain the type of the return
  const setActivePinia = (pinia) => (activePinia = pinia);
  /**
   * Get the currently active pinia if there is any.
   */
  const getActivePinia = () => (vueDemi.hasInjectionContext() && vueDemi.inject(piniaSymbol)) || activePinia;
  const piniaSymbol = (Symbol('pinia') );

  function getDevtoolsGlobalHook() {
    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;
  }
  function getTarget() {
    // @ts-ignore
    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')
      ? window
      : typeof self === 'object' && self.self === self
      ? self
      : typeof global !== 'undefined'
      ? global
      : typeof globalThis === 'object'
      ? globalThis
      : { HTMLElement: null };
  }
  const isProxyAvailable = typeof Proxy === 'function';

  const HOOK_SETUP = 'devtools-plugin:setup';
  const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';

  let supported;
  let perf;
  function isPerformanceSupported() {
    var _a;
    if (supported !== undefined) {
      return supported;
    }
    if (typeof window !== 'undefined' && window.performance) {
      supported = true;
      perf = window.performance;
    } else if (typeof global !== 'undefined' && ((_a = global.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {
      supported = true;
      perf = global.perf_hooks.performance;
    } else {
      supported = false;
    }
    return supported;
  }
  function now() {
    return isPerformanceSupported() ? perf.now() : Date.now();
  }

  class ApiProxy {
    constructor(plugin, hook) {
      this.target = null;
      this.targetQueue = [];
      this.onQueue = [];
      this.plugin = plugin;
      this.hook = hook;
      const defaultSettings = {};
      if (plugin.settings) {
        for (const id in plugin.settings) {
          const item = plugin.settings[id];
          defaultSettings[id] = item.defaultValue;
        }
      }
      const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;
      let currentSettings = Object.assign({}, defaultSettings);
      try {
        const raw = localStorage.getItem(localSettingsSaveId);
        const data = JSON.parse(raw);
        Object.assign(currentSettings, data);
      } catch (e) {
        // noop
      }
      this.fallbacks = {
        getSettings() {
          return currentSettings;
        },
        setSettings(value) {
          try {
            localStorage.setItem(localSettingsSaveId, JSON.stringify(value));
          } catch (e) {
            // noop
          }
          currentSettings = value;
        },
        now() {
          return now();
        },
      };
      if (hook) {
        hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {
          if (pluginId === this.plugin.id) {
            this.fallbacks.setSettings(value);
          }
        });
      }
      this.proxiedOn = new Proxy({}, {
        get: (_target, prop) => {
          if (this.target) {
            return this.target.on[prop];
          } else {
            return (...args) => {
              this.onQueue.push({
                method: prop,
                args,
              });
            };
          }
        },
      });
      this.proxiedTarget = new Proxy({}, {
        get: (_target, prop) => {
          if (this.target) {
            return this.target[prop];
          } else if (prop === 'on') {
            return this.proxiedOn;
          } else if (Object.keys(this.fallbacks).includes(prop)) {
            return (...args) => {
              this.targetQueue.push({
                method: prop,
                args,
                resolve: () => { },
              });
              return this.fallbacks[prop](...args);
            };
          } else {
            return (...args) => {
              return new Promise(resolve => {
                this.targetQueue.push({
                  method: prop,
                  args,
                  resolve,
                });
              });
            };
          }
        },
      });
    }
    async setRealTarget(target) {
      this.target = target;
      for (const item of this.onQueue) {
        this.target.on[item.method](...item.args);
      }
      for (const item of this.targetQueue) {
        item.resolve(await this.target[item.method](...item.args));
      }
    }
  }

  function setupDevtoolsPlugin(pluginDescriptor, setupFn) {
    const descriptor = pluginDescriptor;
    const target = getTarget();
    const hook = getDevtoolsGlobalHook();
    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;
    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {
      hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);
    } else {
      const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;
      const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];
      list.push({
        pluginDescriptor: descriptor,
        setupFn,
        proxy,
      });
      if (proxy)
        setupFn(proxy.proxiedTarget);
    }
  }

  function isPlainObject(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  o) {
    return (o &&
      typeof o === 'object' &&
      Object.prototype.toString.call(o) === '[object Object]' &&
      typeof o.toJSON !== 'function');
  }

  // type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }
  // TODO: can we change these to numbers?
  /**
   * Possible types for SubscriptionCallback
   */
  exports.MutationType = void 0;
  (function (MutationType) {
    /**
     * Direct mutation of the state:
     *
     * - `store.name = 'new name'`
     * - `store.$state.name = 'new name'`
     * - `store.list.push('new item')`
     */
    MutationType["direct"] = "direct";
    /**
     * Mutated the state with `$patch` and an object
     *
     * - `store.$patch({ name: 'newName' })`
     */
    MutationType["patchObject"] = "patch object";
    /**
     * Mutated the state with `$patch` and a function
     *
     * - `store.$patch(state => state.name = 'newName')`
     */
    MutationType["patchFunction"] = "patch function";
    // maybe reset? for $state = {} and $reset
  })(exports.MutationType || (exports.MutationType = {}));

  const IS_CLIENT = typeof window !== 'undefined';
  /**
   * Should we add the devtools plugins.
   * - only if dev mode or forced through the prod devtools flag
   * - not in test
   * - only if window exists (could change in the future)
   */
  const USE_DEVTOOLS = IS_CLIENT;

  /*
   * FileSaver.js A saveAs() FileSaver implementation.
   *
   * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin
   * Morote.
   *
   * License : MIT
   */
  // The one and only way of getting global scope in all environments
  // https://stackoverflow.com/q/3277182/1008999
  const _global = /*#__PURE__*/ (() => typeof window === 'object' && window.window === window
    ? window
    : typeof self === 'object' && self.self === self
    ? self
    : typeof global === 'object' && global.global === global
    ? global
    : typeof globalThis === 'object'
    ? globalThis
    : { HTMLElement: null })();

  function bom(blob, { autoBom = false } = {}) {
    // prepend BOM for UTF-8 XML and text/* types (including HTML)
    // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF
    if (autoBom &&
      /^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(blob.type)) {
      return new Blob([String.fromCharCode(0xfeff), blob], { type: blob.type });
    }
    return blob;
  }
  function download(url, name, opts) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    xhr.onload = function () {
      saveAs(xhr.response, name, opts);
    };
    xhr.onerror = function () {
      console.error('could not download file');
    };
    xhr.send();
  }
  function corsEnabled(url) {
    const xhr = new XMLHttpRequest();
    // use sync to avoid popup blocker
    xhr.open('HEAD', url, false);
    try {
      xhr.send();
    } catch (e) { }
    return xhr.status >= 200 && xhr.status <= 299;
  }
  // `a.click()` doesn't work for all browsers (#465)
  function click(node) {
    try {
      node.dispatchEvent(new MouseEvent('click'));
    } catch (e) {
      const evt = document.createEvent('MouseEvents');
      evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);
      node.dispatchEvent(evt);
    }
  }
  const _navigator = typeof navigator === 'object' ? navigator : { userAgent: '' };
  // Detect WebView inside a native macOS app by ruling out all browsers
  // We just need to check for 'Safari' because all other browsers (besides Firefox) include that too
  // https://www.whatismybrowser.com/guides/the-latest-user-agent/macos
  const isMacOSWebView = /*#__PURE__*/ (() => /Macintosh/.test(_navigator.userAgent) &&
    /AppleWebKit/.test(_navigator.userAgent) &&
    !/Safari/.test(_navigator.userAgent))();
  const saveAs = !IS_CLIENT
    ? () => { } // noop
    : // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program
    typeof HTMLAnchorElement !== 'undefined' &&
      'download' in HTMLAnchorElement.prototype &&
      !isMacOSWebView
    ? downloadSaveAs
    : // Use msSaveOrOpenBlob as a second approach
    'msSaveOrOpenBlob' in _navigator
    ? msSaveAs
    : // Fallback to using FileReader and a popup
    fileSaverSaveAs;
  function downloadSaveAs(blob, name = 'download', opts) {
    const a = document.createElement('a');
    a.download = name;
    a.rel = 'noopener'; // tabnabbing
    // TODO: detect chrome extensions & packaged apps
    // a.target = '_blank'
    if (typeof blob === 'string') {
      // Support regular links
      a.href = blob;
      if (a.origin !== location.origin) {
        if (corsEnabled(a.href)) {
          download(blob, name, opts);
        } else {
          a.target = '_blank';
          click(a);
        }
      } else {
        click(a);
      }
    } else {
      // Support blobs
      a.href = URL.createObjectURL(blob);
      setTimeout(function () { URL.revokeObjectURL(a.href); }, 4e4); // 40s
      setTimeout(function () { click(a); }, 0);
    }
  }
  function msSaveAs(blob, name = 'download', opts) {
    if (typeof blob === 'string') {
      if (corsEnabled(blob)) {
        download(blob, name, opts);
      } else {
        const a = document.createElement('a');
        a.href = blob;
        a.target = '_blank';
        setTimeout(function () { click(a); });
      }
    } else {
      // @ts-ignore: works on windows
      navigator.msSaveOrOpenBlob(bom(blob, opts), name);
    }
  }
  function fileSaverSaveAs(blob, name, opts, popup) {
    // Open a popup immediately do go around popup blocker
    // Mostly only available on user interaction and the fileReader is async so...
    popup = popup || open('', '_blank');
    if (popup) {
      popup.document.title = popup.document.body.innerText = 'downloading...';
    }
    if (typeof blob === 'string')
      return download(blob, name, opts);
    const force = blob.type === 'application/octet-stream';
    const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;
    const isChromeIOS = /CriOS\/[\d]+/.test(navigator.userAgent);
    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) && typeof FileReader !== 'undefined') {
      // Safari doesn't allow downloading of blob URLs
      const reader = new FileReader();
      reader.onloadend = function () {
        let url = reader.result;
        if (typeof url !== 'string') {
          popup = null;
          throw new Error('Wrong reader.result type');
        }
        url = isChromeIOS
          ? url
          : url.replace(/^data:[^;]*;/, 'data:attachment/file;');
        if (popup) {
          popup.location.href = url;
        } else {
          location.assign(url);
        }
        popup = null; // reverse-tabnabbing #460
      };
      reader.readAsDataURL(blob);
    } else {
      const url = URL.createObjectURL(blob);
      if (popup)
        popup.location.assign(url);
      else
        location.href = url;
      popup = null; // reverse-tabnabbing #460
      setTimeout(function () { URL.revokeObjectURL(url); }, 4e4); // 40s
    }
  }

  /**
   * Shows a toast or console.log
   *
   * @param message - message to log
   * @param type - different color of the tooltip
   */
  function toastMessage(message, type) {
    const piniaMessage = '🍍 ' + message;
    if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {
      // No longer available :(
      __VUE_DEVTOOLS_TOAST__(piniaMessage, type);
    } else if (type === 'error') {
      console.error(piniaMessage);
    } else if (type === 'warn') {
      console.warn(piniaMessage);
    } else {
      console.log(piniaMessage);
    }
  }
  function isPinia(o) {
    return '_a' in o && 'install' in o;
  }

  /**
   * This file contain devtools actions, they are not Pinia actions.
   */
  // ---
  function checkClipboardAccess() {
    if (!('clipboard' in navigator)) {
      toastMessage(`Your browser doesn't support the Clipboard API`, 'error');
      return true;
    }
  }
  function checkNotFocusedError(error) {
    if (error instanceof Error && error.message.toLowerCase().includes('document is not focused')) {
      toastMessage('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.', 'warn');
      return true;
    }
    return false;
  }

  /**
   * Creates a Pinia instance to be used by the application
   */
  function createPinia() {
    const scope = vueDemi.effectScope(true);
    // NOTE: here we could check the window object for a state and directly set it
    // if there is anything like it with Vue 3 SSR
    const state = scope.run(() => vueDemi.ref({}));
    let _p = []; // plugins added before calling app.use(pinia)
    let toBeInstalled = [];
    const pinia = vueDemi.markRaw({
      install(app) {
        // this allows calling useStore() outside of a component setup after
        // installing pinia's plugin
        setActivePinia(pinia);
        if (!vueDemi.isVue2) {
          pinia._a = app;
          app.provide(piniaSymbol, pinia);
          app.config.globalProperties.$pinia = pinia;
          /* istanbul ignore else */
          if (USE_DEVTOOLS) {
            // registerPiniaDevtools(app, pinia);
          }
          toBeInstalled.forEach((plugin) => _p.push(plugin));
          toBeInstalled = [];
        }
      },
      use(plugin) {
        if (!this._a && !vueDemi.isVue2) {
          toBeInstalled.push(plugin);
        } else {
          _p.push(plugin);
        }
        return this;
      },
      _p,
      // it's actually undefined here
      // @ts-expect-error
      _a: null,
      _e: scope,
      _s: new Map(),
      state,
    });
    return pinia;
  }

  /**
   * Checks if a function is a `StoreDefinition`.
   *
   * @param fn - object to test
   * @returns true if `fn` is a StoreDefinition
   */
  const isUseStore = (fn) => {
    return typeof fn === 'function' && typeof fn.$id === 'string';
  };

  function defineStore(idOrOptions, setup, setupOptions) {
    let id;
    let options;
    const isSetupStore = typeof setup === 'function';
    if (typeof idOrOptions === 'string') {
      id = idOrOptions;
      // the option store setup will contain the actual options in this case
      options = isSetupStore ? setupOptions : setup;
    } else {
      options = idOrOptions;
      id = idOrOptions.id;
      if (typeof id !== 'string') {
        throw new Error(`[🍍]: "defineStore()" must be passed a store id as its first argument.`);
      }
    }

    function useStore(pinia, hot) {
      const hasContext = vueDemi.hasInjectionContext();
      pinia = (pinia) || (hasContext ? vueDemi.inject(piniaSymbol, null) : null);
      if (pinia) setActivePinia(pinia);
      if (!activePinia) {
        throw new Error(`[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?\n` +
          `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\n` +
          `This will fail in production.`);
      }
      pinia = activePinia;
      if (!pinia._s.has(id)) {
        // creating the store registers it in `pinia._s`
        if (isSetupStore) {
          // createSetupStore(id, setup, options, pinia);
        } else {
          // createOptionsStore(id, options, pinia);
        }
        /* istanbul ignore else */
        {
          // @ts-expect-error: not the right inferred type
          useStore._pinia = pinia;
        }
      }
      const store = pinia._s.get(id);
      return store;
    }
    useStore.$id = id;
    return useStore;
  }

  // Export all the functions
  exports.createPinia = createPinia;
  exports.defineStore = defineStore;
  exports.getActivePinia = getActivePinia;
  exports.setActivePinia = setActivePinia;

  return exports;
})({});
