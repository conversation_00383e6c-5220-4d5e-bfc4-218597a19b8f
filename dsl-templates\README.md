# VTJ DSL 模板库

这个文件夹包含了各种 VTJ 低代码平台的 DSL 模板，可以直接导入到 VTJ 平台中使用。

## 📁 模板列表

### 1. 数据统计管理页面 (`data-statistics-dashboard.json`)

一个完整的数据统计管理页面模板，包含以下功能：

#### 🎯 页面结构
- **侧边栏导航**：深色主题的导航菜单
  - 仪表盘
  - 用户管理
  - 订单管理
  - 商品管理
  - 财务管理
  - 报表分析

- **主内容区域**：白色背景的内容展示区

#### 📊 统计卡片区域
4个渐变色统计卡片，展示关键业务指标：
- **今日访问**：蓝紫渐变 (#667eea → #764ba2)
- **销售额**：粉红渐变 (#f093fb → #f5576c)
- **用户数**：蓝青渐变 (#4facfe → #00f2fe)
- **订单数**：绿青渐变 (#43e97b → #38f9d7)

#### 📈 图表区域
- 预留图表容器，支持集成 ECharts 等图表库
- 显示"今日访问趋势"数据
- 虚线边框的占位符设计

#### 📝 数据录入表单
包含完整的表单字段：
- **数据名称**：文本输入框
- **数据类型**：下拉选择（访问数据/销售数据/用户数据）
- **数值**：数字输入框
- **日期**：日期选择器
- **备注**：多行文本框
- **操作按钮**：提交数据、重置

#### 🎨 设计特点
- **现代化设计**：使用渐变色和卡片阴影
- **响应式布局**：基于 Element Plus 的栅格系统
- **统一配色**：深色侧边栏 + 白色主体 + 彩色卡片
- **图标装饰**：使用 Emoji 图标增强视觉效果

## 🚀 使用方法

### 1. 导入到 VTJ 平台
1. 登录 VTJ 低代码平台
2. 创建新应用或打开现有应用
3. 在设计器中选择"导入 DSL"
4. 复制对应的 JSON 内容并粘贴
5. 点击确认导入

### 2. 自定义修改
导入后可以通过设计器进行以下修改：
- **调整颜色**：修改卡片渐变色、主题色等
- **更换图标**：替换菜单和卡片中的图标
- **修改布局**：调整栅格比例、间距等
- **添加交互**：为按钮和菜单添加点击事件
- **集成图表**：在图表区域集成真实的图表组件

### 3. 数据绑定
- 将统计卡片的数值绑定到真实数据源
- 为表单添加提交逻辑
- 连接后端 API 接口

## 📋 技术要求

### 依赖组件
此模板使用了以下 Element Plus 组件：
- `ElContainer` / `ElAside` / `ElMain`：布局容器
- `ElMenu` / `ElMenuItem`：导航菜单
- `ElRow` / `ElCol`：栅格布局
- `ElCard`：卡片容器
- `ElForm` / `ElFormItem`：表单组件
- `ElInput` / `ElInputNumber`：输入框
- `ElSelect` / `ElOption`：下拉选择
- `ElDatePicker`：日期选择器
- `ElButton`：按钮

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🔧 扩展建议

### 1. 图表集成
推荐集成以下图表库：
- **ECharts**：功能强大的图表库
- **Chart.js**：轻量级图表库
- **D3.js**：自定义图表开发

### 2. 数据可视化增强
- 添加更多统计维度
- 实现实时数据更新
- 增加数据筛选功能

### 3. 交互功能
- 菜单路由跳转
- 表单验证
- 数据导出功能
- 权限控制

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 查看 VTJ 官方文档
2. 在项目 Issues 中提问
3. 联系技术支持团队

## 📄 许可证

本模板库遵循 MIT 许可证，可自由使用和修改。
