import { createApp } from 'vue';
import {
  createProvider,
  NodeEnv,
  ContextMode,
  Access,
  Provider,
  parseFunction
} from '@vtj/renderer';
import {
  createUniAppComponent,
  createUniRoutes,
  setupUniApp,
  notify,
  loading,
  alert
} from '@vtj/uni';
import {
  LcdpService,
  STORAGE_KEY,
  ACCESS_PRIVATE_KEY,
  MATERIAL_PATH,
  AUTH_PATH,
  setGlobalRequest,
  appGuard
} from '@/shared';
import NotFound from './NotFound.vue';

// uniapp的页面路由为： /pages/应用Id/页面Id
const id = location.hash.split('/')[2];
const service = new LcdpService(notify);
const request = setGlobalRequest({ notify, loading });

const access = new Access({
  alert,
  storageKey: STORAGE_KEY,
  privateKey: ACCESS_PRIVATE_KEY,
  auth: AUTH_PATH
});

access.connect({ request, mode: ContextMode.Runtime });

const init = async (provider: Provider) => {
  const { Vue, UniH5 } = window as any;
  const project = provider.project;
  if (!project || !Vue || !UniH5) return;

  const App = createUniAppComponent(project.uniConfig || {}, (script) =>
    parseFunction(script, window, false, true)
  );

  const uniConfig = project.uniConfig || {};
  const { css, pagesJson, manifestJson } = uniConfig;
  const basePath = `/pages/${id}`;
  const routes = await createUniRoutes(provider, true, basePath);
  // 设计器没有页面时，显示404
  if (routes.length === 0) {
    routes.push({
      id: 'NotFound',
      path: basePath,
      component: NotFound as any
    });
  }
  const app = setupUniApp({
    Vue,
    App,
    UniH5,
    routes,
    css,
    pagesJson,
    manifestJson
  });

  app.use(provider);
  app.mount(document.body);
  await appGuard(id, access);
};

if (id) {
  const { provider, onReady } = createProvider({
    nodeEnv: NodeEnv.Production,
    mode: ContextMode.Runtime,
    materialPath: MATERIAL_PATH,
    service,
    project: { id },
    // 提供页面上下文数据，解决未定义变量的问题
    context: {
      // 用户信息
      currentUser: {
        id: 1,
        name: '演示用户',
        email: '<EMAIL>',
        avatar: '👤',
        role: 'admin'
      },
      // 快捷方式数据
      shortcuts: [
        { name: '首页', url: '/', icon: '🏠' },
        { name: '设置', url: '/settings', icon: '⚙️' },
        { name: '帮助', url: '/help', icon: '❓' }
      ],
      // 处方统计数据
      prescriptionStats: {
        total: 0,
        pending: 0,
        completed: 0,
        cancelled: 0
      },
      // 下拉菜单处理函数
      handleDropdownCommand: (command: string) => {
        console.log('下拉菜单命令:', command);
        // 这里可以根据命令执行相应的操作
        switch (command) {
          case 'profile':
            console.log('查看个人资料');
            break;
          case 'settings':
            console.log('打开设置');
            break;
          case 'logout':
            console.log('退出登录');
            break;
          default:
            console.log('未知命令:', command);
        }
      }
    }
  });

  onReady(() => init(provider));
} else {
  const app = createApp(NotFound);
  app.mount(document.body);
}
