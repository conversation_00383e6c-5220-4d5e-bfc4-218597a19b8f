// 测试代码转换服务
import fs from 'fs';

// 使用简单的Vue内容进行测试
const vueContent = `<template>
  <div>
    <h1>Hello World</h1>
    <p>This is a test</p>
  </div>
</template>

<script>
export default {
  name: 'TestComponent'
}
</script>`;

// 构造请求数据
const requestData = {
  id: "123",
  name: "123",
  source: vueContent,
  project: {
    id: "asdfasdf"
  }
};

// 发送请求到代码转换服务
fetch('http://localhost:3001/api/parser/vue', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('转换结果:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('转换失败:', error);
});
