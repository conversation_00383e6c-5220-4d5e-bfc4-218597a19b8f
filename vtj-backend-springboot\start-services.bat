@echo off
echo ========================================
echo    启动 VTJ 低代码平台后端服务
echo ========================================

echo.
echo [1/3] 检查环境...

:: 检查 Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java 未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Java 环境正常

:: 检查 Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Node.js 环境正常

:: 检查 MySQL
echo ⚠️  请确保 MySQL 服务已启动，数据库 vtj_db 已创建

echo.
echo [2/3] 启动代码转换服务...
cd vtj-coder-service

:: 检查是否已安装依赖
if not exist node_modules (
    echo 📦 安装 Node.js 依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

:: 启动 Node.js 服务
echo 🚀 启动代码转换服务 (端口: 3001)...
start "VTJ Coder Service" cmd /k "npm start"

:: 等待服务启动
timeout /t 3 /nobreak >nul

echo.
echo [3/3] 启动 Spring Boot 服务...
cd ..

:: 启动 Spring Boot 服务
echo 🚀 启动 Spring Boot 服务 (端口: 8080)...
start "VTJ Spring Boot" cmd /k "mvn spring-boot:run"

echo.
echo ========================================
echo    服务启动完成！
echo ========================================
echo.
echo 📍 代码转换服务: http://localhost:3001
echo 📍 Spring Boot API: http://localhost:8080/api
echo 📍 健康检查: http://localhost:3001/health
echo.
echo 💡 提示：
echo    - 两个服务窗口会自动打开
echo    - 关闭窗口即可停止对应服务
echo    - 确保 MySQL 服务正在运行
echo.
pause
