<template>
  <ElDialog ref="dialog" :model-value="true" :title="dialogTitle">
    <ElForm ref="form" label-width="100px" :model="model" :rules="rules">
      <ElFormItem label="所属平台" prop="platform">
        <ElRadioGroup v-model="model.platform" :disabled="!!model.id">
          <ElRadioButton label="Web" value="Web"></ElRadioButton>
          <ElRadioButton label="H5" value="H5"></ElRadioButton>
          <ElRadioButton label="UniApp" value="UniApp"></ElRadioButton>
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="应用名称" prop="name">
        <ElInput
          v-model.trim="model.name"
          :maxlength="50"
          :disabled="!!model.id">
          <template v-if="hasPrefix" #prepend>{{ prefix }}</template>
        </ElInput>
      </ElFormItem>
      <ElFormItem label="应用标题" prop="label">
        <ElInput v-model.trim="model.label"></ElInput>
      </ElFormItem>
      <ElFormItem label="应用权限" prop="platform">
        <ElRadioGroup v-model="model.scope">
          <ElRadioButton label="受限" value="protected"></ElRadioButton>
          <ElRadioButton label="公开" value="public"></ElRadioButton>
          <ElRadioButton label="私密" value="private"></ElRadioButton>
        </ElRadioGroup>
        <ElAlert :title="scopeTip" type="warning" :closable="false"></ElAlert>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">取消</ElButton>
      <ElButton type="primary" @click="onSubmit">提交</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import { useTemplateRef, reactive, computed } from 'vue';
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElRadioGroup,
  ElRadioButton,
  ElAlert,
  type DialogInstance,
  type FormInstance
} from 'element-plus';

import type { LowCodeAppVO } from '@/shared';
import { saveLowCodeApp } from '@/apis';
import { usePrefix } from '@/hooks';
import { notify } from '@/utils';

export interface Props {
  data?: LowCodeAppVO | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  submit: [data: LowCodeAppVO];
}>();
const dialog = useTemplateRef<DialogInstance>('dialog');
const form = useTemplateRef<FormInstance>('form');
const { hasPrefix, prefix, skipPrefix, withPrefix } = usePrefix(props.data);
const model = reactive<LowCodeAppVO>({
  label: '',
  platform: 'Web',
  scope: 'protected',
  ...(props.data || {}),
  name: skipPrefix(props.data?.name || '')
});

const rules = {
  platform: [{ required: true, message: '应用名称是必填项' }],
  name: [
    { required: true, message: '应用名称是必填项' },
    {
      message: '名称格式错误,名称为英文驼峰格式',
      pattern: /^[A-Za-z_$][\:\w_-]*$/
    }
  ],
  label: [{ required: true, message: '应用标题是必填项' }]
};

const dialogTitle = computed(() => {
  return props.data?.id ? '编辑应用' : '创建应用';
});

const scopeTip = computed(() => {
  const tips = {
    public: '无需任何身份验证即可访问该应用',
    private: '仅限自己访问',
    protected: '只允许登录的用户都可以访问该应用'
  };
  return tips[model.scope] || tips.protected;
});

const onCancel = () => {
  dialog.value?.handleClose();
};

const onSubmit = async () => {
  console.log('开始提交应用表单...');

  const valid = await form.value?.validate().catch((error) => {
    console.error('表单验证失败:', error);
    notify('表单验证失败，请检查输入内容', '错误', 'error');
    return false;
  });

  if (valid) {
    const data: LowCodeAppVO = {
      ...model,
      name: withPrefix(model.name)
    };
    console.log('准备保存应用数据:', data);

    // 转换数据格式以匹配后端期望
    const requestData = {
      name: data.name,
      label: data.label,
      platform: typeof data.platform === 'string' ? data.platform : (data.platform as any)?.value || data.platform,
      scope: data.scope,
      userId: data.userId,
      ...(props.data?.id && { id: props.data.id }) // 编辑模式下添加id字段
    };
    console.log('转换后的请求数据:', requestData);

    try {
      const ret = await saveLowCodeApp(requestData);
      console.log('保存应用成功:', ret);

      if (ret) {
        // notify('应用保存成功！', '成功', 'success'); // 注释掉成功提示
        emit('submit', data);
        dialog.value?.handleClose();
      } else {
        console.error('保存应用失败: 返回值为空');
        notify('保存应用失败：服务器返回空数据', '错误', 'error');
      }
    } catch (error) {
      console.error('保存应用出错:', error);

      // 提取错误信息
      let errorMessage = '保存应用失败，请稍后重试';
      if (error && typeof error === 'object') {
        const err = error as any;
        if (err.message) {
          errorMessage = err.message;
        } else if (err.data && err.data.message) {
          errorMessage = err.data.message;
        } else if (err.response && err.response.data && err.response.data.message) {
          errorMessage = err.response.data.message;
        }
      }

      notify(errorMessage, '错误', 'error');
    }
  } else {
    console.log('表单验证未通过');
  }
};
</script>

<style lang="scss" scoped>
.el-alert {
  margin-top: 10px;
}
</style>
