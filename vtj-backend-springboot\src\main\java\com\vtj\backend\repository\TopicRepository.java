package com.vtj.backend.repository;

import com.vtj.backend.entity.Topic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * AI话题数据访问层
 */
@Repository
public interface TopicRepository extends JpaRepository<Topic, Long> {

    /**
     * 根据话题ID查找
     */
    Optional<Topic> findByTopicId(String topicId);

    /**
     * 根据用户ID查找话题列表
     */
    List<Topic> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据类型查找话题
     */
    List<Topic> findByType(String type);

    /**
     * 查找热门话题
     */
    @Query("SELECT t FROM Topic t WHERE t.status = 1 ORDER BY t.createdAt DESC")
    List<Topic> findHotTopics();

    /**
     * 根据文件ID查找话题
     */
    @Query("SELECT t FROM Topic t WHERE t.fileUrl LIKE %:fileId%")
    List<Topic> findByFileId(@Param("fileId") String fileId);
}
