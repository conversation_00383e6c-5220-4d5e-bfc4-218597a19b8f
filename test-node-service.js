import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3002;

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'test-vtj-coder-service',
    timestamp: new Date().toISOString()
  });
});

// Vue 代码转 DSL (简化版本)
app.post('/api/parser/vue', async (req, res) => {
  try {
    const { id, name, source } = req.body;

    console.log('收到 Vue 转 DSL 请求:', {
      id,
      name,
      hasSource: !!source,
      sourceLength: source ? source.length : 0
    });

    // 简化的DSL转换
    const dsl = {
      type: 'page',
      id: id,
      name: name,
      title: name,
      meta: {
        title: `${name} 页面`,
        description: '通过本地同步工具创建的页面'
      },
      template: source ? extractTemplate(source) : '',
      script: source ? extractScript(source) : '',
      style: source ? extractStyle(source) : '',
      components: [],
      data: {},
      methods: {},
      computed: {},
      lifecycle: {}
    };

    res.json({
      success: true,
      dsl: dsl,
      message: 'Vue 转 DSL 成功'
    });

  } catch (error) {
    console.error('Vue 转 DSL 失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Vue 转 DSL 失败'
    });
  }
});

// 提取template部分
function extractTemplate(vueContent) {
  const match = vueContent.match(/<template>([\s\S]*?)<\/template>/);
  return match ? match[1].trim() : '';
}

// 提取script部分
function extractScript(vueContent) {
  const match = vueContent.match(/<script[^>]*>([\s\S]*?)<\/script>/);
  return match ? match[1].trim() : '';
}

// 提取style部分
function extractStyle(vueContent) {
  const match = vueContent.match(/<style[^>]*>([\s\S]*?)<\/style>/);
  return match ? match[1].trim() : '';
}

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: error.message,
    message: '服务器内部错误'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 测试代码转换服务启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`🔄 Vue转DSL: POST http://localhost:${PORT}/api/parser/vue`);
});
