import { createApp } from 'vue';
import { Access, ContextMode } from '@vtj/pro';
import {
  STORAGE_KEY,
  ACCESS_PRIVATE_KEY,
  AUTH_PATH,
  setGlobalRequest
} from '@/shared';
import { loading, notify, alert, autoAuth } from '@/utils';
import router from './router';
import App from './App.vue';
import './style/index.scss';

const app = createApp(App);
const request = setGlobalRequest({ loading, notify });
const access = new Access({
  alert,
  storageKey: STORAGE_KEY,
  privateKey: ACCESS_PRIVATE_KEY,
  auth: AUTH_PATH
});

// 先安装路由和access插件
app.use(router);
app.use(access);

// 挂载应用后再进行连接和认证
(async function () {
  app.mount('#app');

  // 等待路由器就绪后再连接access和进行认证
  await router.isReady();
  access.connect({ request, router, mode: ContextMode.Design });
  await autoAuth(access);
})();
