import { createApi } from '@vtj/utils';
import { REMOTE } from '@/shared';

/**
 * 用授权码自动登录
 * @param authCode
 * @returns
 */
export const loginBySign = async (authCode: string) => {
  const api = createApi<string[]>({
    baseURL: REMOTE,
    url: '/open/auth/:code',
    method: 'get'
  });

  return await api({}, { params: { code: authCode } });
};

/**
 * 获取当前登录的用户信息
 * @param token
 * @returns
 */
export const getLoginUser = async (token: string) => {
  const api = createApi<string[]>({
    baseURL: REMOTE,
    url: '/open/user/:token',
    method: 'get'
  });
  return await api({}, { params: { token } });
};

/**
 * 获取模版列表
 */
export const getTemplates = async (platform: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/templates',
    method: 'get'
  });
  return await api({ platform, token });
};

/**
 * 获取模版详情
 * @param id
 * @param token
 * @returns
 */
export const getTemplateById = async (id: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/template/:token',
    method: 'get'
  });
  return await api({ id }, { params: { token } });
};

/**
 * 删除模版
 * @param id
 * @param token
 * @returns
 */
export const removeTemplate = async (id: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/template/remove/:token',
    method: 'get'
  });
  return await api({ id }, { params: { token } });
};

/**
 * 获取模版DSL
 * @param id
 * @param token
 * @returns
 */
export const getTemplateDsl = async (id: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/dsl/:token',
    method: 'get'
  });
  return await api({ id }, { params: { token } });
};

/**
 * 获取数据字典项
 * @param code
 * @returns
 */
export const getDictOptions = async (code: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/dict/:code',
    method: 'get'
  });
  return await api(null, { params: { code } });
};

/**
 * 发布模版
 * @param template
 * @param token
 * @returns
 */
export const publishTemplate = async (
  template: Record<string, any>,
  token?: string
) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/template/publish/:token',
    method: 'post',
    settings: {
      type: 'data'
    }
  });
  return await api(template, { params: { token } });
};

/**
 * AI助手发起文本话题
 * @param topic
 * @param token
 * @returns
 */
export const postTopic = async (topic: Record<string, any>, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/post/:token',
    method: 'post',
    settings: {
      type: 'json',
      originResponse: true
    }
  });
  return await api(topic, { params: { token } });
};

/**
 * AI助手 发起图片类型话题
 * @param topic
 * @param token
 * @returns
 */
export const postImageTopic = async (
  topic: Record<string, any>,
  token?: string
) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/image/:token',
    method: 'post',
    settings: {
      type: 'data',
      originResponse: true
    }
  });
  return await api(topic, { params: { token } });
};

/**
 * AI助手 发起元数据话题
 * @param topic
 * @param token
 * @returns
 */
export const postJsonTopic = async (
  topic: Record<string, any>,
  token?: string
) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/json/:token',
    method: 'post',
    settings: {
      type: 'data',
      originResponse: true
    }
  });
  return await api(topic, { params: { token } });
};

/**
 * 获取AI助手对话记录
 * @param topicId
 * @param token
 * @returns
 */
export const getChats = async (topicId: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/chat/list/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id: topicId }, { params: { token } });
};

/**
 * 获取AI助手话题列表
 * @param fileId
 * @param token
 * @returns
 */
export const getTopics = async (fileId: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/list/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id: fileId }, { params: { token } });
};

/**
 * 发送AI助手对话
 * @param chat
 * @param token
 * @returns
 */
export const postChat = async (chat: Record<string, any>, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/chat/post/:token',
    method: 'post',
    settings: {
      type: 'json',
      originResponse: true
    }
  });
  return await api(chat, { params: { token } });
};

/**
 * 更新AI助手对话
 * @param chat
 * @param token
 * @returns
 */
export const saveChat = async (chat: Record<string, any>, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/chat/save/:token',
    method: 'post',
    settings: {
      type: 'json',
      originResponse: true
    }
  });
  return await api(chat, { params: { token } });
};

/**
 * 取消AI对话
 * @param chatId
 * @param token
 * @returns
 */
export const cancelChat = async (chatId: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/chat/cancel/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id: chatId }, { params: { token } });
};

/**
 * 删除AI助手话题
 * @param topicId
 * @param token
 * @returns
 */
export const removeTopic = async (topicId: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/remove/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id: topicId }, { params: { token } });
};

/**
 * 获取热门话题
 * @returns
 */
export const getHotTopics = async () => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/topic/hot',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api();
};

/**
 * AI对话流式请求
 * @param topicId
 * @param chatId
 * @param token
 * @returns
 */
export const chatCompletions = (
  topicId: string,
  chatId: string,
  token?: string
) => {
  const api = `${REMOTE}/open/completions/${token}?tid=${topicId}&id=${chatId}`;
  const controller = new AbortController();
  const signal = controller.signal;
  return {
    abort: () => controller.abort(),
    promise: fetch(api, {
      method: 'get',
      signal
    })
  };
};

/**
 * 获取系统配置信息
 * @param token
 * @returns
 */
export const getSettins = async (token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/settings/:token',
    method: 'get',
    settings: {
      type: 'form'
    }
  });
  return await api(null, { params: { token } });
};

/**
 * 新建订单
 * @param token
 * @returns
 */
export const createOrder = async (token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/order/:token',
    method: 'post',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api(null, { params: { token } });
};

/**
 * 取消订单
 * @param id
 * @param token
 * @returns
 */
export const cancelOrder = async (id: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/order/cancel/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id }, { params: { token } });
};

/**
 * 获取订单详情
 * @param id
 * @param token
 * @returns
 */
export const getOrder = async (id: string, token?: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/open/order/:token',
    method: 'get',
    settings: {
      type: 'form',
      originResponse: true
    }
  });
  return await api({ id }, { params: { token } });
};
