{"name": "vtj-project-uniapp", "version": "0.9.30", "private": true, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "cross-env VITE_CJS_IGNORE_WARNING=true uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "cross-env VITE_CJS_IGNORE_WARNING=true uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "clean": "vtj rm ./package-lock.json && vtj rm ./pnpm-lock.yaml && vtj rm ./dist && vtj rm ./node_modules", "preview:h5": "cross-env VITE_CJS_IGNORE_WARNING=true PREVIEW=true vite preview", "dev": "cross-env ENV_TYPE=local npm run dev:h5", "build": "npm run h5:prod", "preview": "npm run preview:h5", "h5:sit": "cross-env ENV_TYPE=sit npm run build:h5", "h5:uat": "cross-env ENV_TYPE=uat npm run build:h5", "h5:prod": "cross-env ENV_TYPE=live npm run build:h5"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4050720250324001", "@dcloudio/uni-app-plus": "3.0.0-4050720250324001", "@dcloudio/uni-components": "3.0.0-4050720250324001", "@dcloudio/uni-h5": "3.0.0-4050720250324001", "@dcloudio/uni-mp-alipay": "3.0.0-4050720250324001", "@dcloudio/uni-mp-baidu": "3.0.0-4050720250324001", "@dcloudio/uni-mp-jd": "3.0.0-4050720250324001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4050720250324001", "@dcloudio/uni-mp-lark": "3.0.0-4050720250324001", "@dcloudio/uni-mp-qq": "3.0.0-4050720250324001", "@dcloudio/uni-mp-toutiao": "3.0.0-4050720250324001", "@dcloudio/uni-mp-weixin": "3.0.0-4050720250324001", "@dcloudio/uni-quickapp-webview": "3.0.0-4050720250324001", "@dcloudio/uni-ui": "~1.5.3", "vue-i18n": "~11.0.1", "@vtj/uni-app": "latest"}, "devDependencies": {"@dcloudio/types": "~3.4.7", "@dcloudio/uni-automator": "3.0.0-4050720250324001", "@dcloudio/uni-cli-shared": "3.0.0-4050720250324001", "@dcloudio/uni-stacktracey": "3.0.0-4050720250324001", "@dcloudio/vite-plugin-uni": "3.0.0-4050720250324001", "@uni-helper/uni-app-types": "~0.5.13", "@uni-helper/uni-ui-types": "~0.5.13", "@vue/runtime-core": "~3.5.5", "miniprogram-api-typings": "~4.0.2", "@vtj/cli": "latest", "@vtj/pro": "latest", "@vtj/local": "latest"}, "vtj": {"platform": "uniapp"}}