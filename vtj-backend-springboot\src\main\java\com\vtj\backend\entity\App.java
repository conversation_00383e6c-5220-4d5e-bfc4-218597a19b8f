package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 应用实体类
 */
@Data
@Entity
@Table(name = "`apps`")
@EqualsAndHashCode(callSuper = true)
public class App extends BaseEntity {

    /**
     * 应用ID
     */
    @Column(name = "app_id", unique = true, nullable = false, length = 50)
    private String appId;

    /**
     * 应用名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 应用描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 应用图标
     */
    @Column(length = 500)
    private String icon;

    /**
     * 平台类型: Web/H5/UniApp
     */
    @Column(length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'Web'")
    private String platform = "Web";

    /**
     * 权限范围: public/protected/private
     */
    @Column(length = 20, columnDefinition = "VARCHAR(20) DEFAULT 'protected'")
    private String scope = "protected";

    /**
     * 状态: 0-禁用 1-启用
     */
    @Column(columnDefinition = "TINYINT DEFAULT 1")
    private Integer status = 1;

    /**
     * 创建用户ID
     */
    @Column(name = "user_id")
    private Long userId;
} 