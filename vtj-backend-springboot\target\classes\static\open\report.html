<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用报告 - VTJ Platform</title>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .report-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        .status.success {
            background-color: #28a745;
        }
        .status.error {
            background-color: #dc3545;
        }
        .status.warning {
            background-color: #ffc107;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VTJ Platform 应用报告</h1>
        
        <div class="report-section">
            <h2>系统状态</h2>
            <p><span class="status success">运行中</span> 系统正常运行</p>
            <p>最后更新时间: <span id="updateTime"></span></p>
        </div>

        <div class="report-section">
            <h2>应用统计</h2>
            <table>
                <thead>
                    <tr>
                        <th>指标</th>
                        <th>数值</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>总应用数</td>
                        <td id="totalApps">-</td>
                        <td><span class="status success">正常</span></td>
                    </tr>
                    <tr>
                        <td>活跃应用数</td>
                        <td id="activeApps">-</td>
                        <td><span class="status success">正常</span></td>
                    </tr>
                    <tr>
                        <td>今日新增</td>
                        <td id="todayApps">-</td>
                        <td><span class="status success">正常</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="report-section">
            <h2>最近错误</h2>
            <div id="errorList">
                <p><span class="status warning">注意</span> 检测到应用ID重复提交问题，已进行优化处理</p>
                <p><span class="status error">错误</span> 静态资源访问问题已修复</p>
            </div>
        </div>

        <div class="report-section">
            <h2>API接口状态</h2>
            <table>
                <thead>
                    <tr>
                        <th>接口</th>
                        <th>状态</th>
                        <th>描述</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>GET /api/apps</td>
                        <td><span class="status success">正常</span></td>
                        <td>获取应用列表</td>
                    </tr>
                    <tr>
                        <td>POST /api/apps</td>
                        <td><span class="status success">正常</span></td>
                        <td>创建应用</td>
                    </tr>
                    <tr>
                        <td>GET /api/apps/{id}</td>
                        <td><span class="status success">正常</span></td>
                        <td>获取应用详情</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 设置更新时间
        document.getElementById('updateTime').textContent = new Date().toLocaleString('zh-CN');
        
        // 模拟加载数据
        setTimeout(() => {
            document.getElementById('totalApps').textContent = '10';
            document.getElementById('activeApps').textContent = '8';
            document.getElementById('todayApps').textContent = '2';
        }, 500);
    </script>
</body>
</html> 