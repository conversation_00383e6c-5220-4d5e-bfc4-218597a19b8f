import {
  createProvider,
  LocalService,
  createModules,
  NodeEnv,
  autoUpdate,
  ContextMode,
  notify,
  loading,
  createAdapter,
  createServiceRequest,
  Startup
} from '@vtj/web';
import { createApp } from 'vue';
import router from './router';
import App from './App.vue';
import { PROJECT_ID, PROJECT_NAME, ROUTER_APPEND_TO } from '@/contants';
import './style/index.scss';

const app = createApp(App);
const adapter = createAdapter({ loading, notify, Startup });
const service = new LocalService(createServiceRequest(notify));

const { provider, onReady } = createProvider({
  nodeEnv: process.env.NODE_ENV as NodeEnv,
  modules: createModules(),
  mode: ContextMode.Raw,
  service,
  adapter,
  router,
  dependencies: {
    Vue: () => import('vue'),
    VueRouter: () => import('vue-router')
  },
  routeAppendTo: ROUTER_APPEND_TO,
  project: {
    id: PROJECT_ID,
    name: PROJECT_NAME
  }
});

onReady(async () => {
  app.use(router);
  app.use(provider);
  app.mount('#app');
});

if (process.env.NODE_ENV === 'production') {
  autoUpdate();
}
