server:
  port: 8080

spring:
  application:
    name: vtj-backend
    
  datasource:
    url: ***********************************************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池配置，解决事务延迟问题
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: false
    properties:
      hibernate:
        format_sql: true
        # 🔧 优化事务和缓存配置
        connection.isolation: 2  # READ_COMMITTED
        cache.use_second_level_cache: false
        cache.use_query_cache: false
        jdbc.batch_size: 20
        order_inserts: true
        order_updates: true
        
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/,classpath:/public/,classpath:/resources/,classpath:/META-INF/resources/
      add-mappings: true
  mvc:
    static-path-pattern: /static/**

# JWT 配置
jwt:
  secret: vtj-platform-secret-key-2024
  expiration: 604800000 # 7天

# 阿里云 OSS 配置
aliyun:
  oss:
    endpoint: oss-cn-shanghai.aliyuncs.com
    accessKeyId: your-access-key-id
    accessKeySecret: your-access-key-secret
    bucketName: vtj-bucket

# VTJ 平台配置
vtj:
  coder:
    service:
      url: http://localhost:3001

# 日志配置
logging:
  level:
    com.vtj.backend: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG