#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const chokidar = require('chokidar');
const chalk = require('chalk');
const ora = require('ora');

/**
 * VTJ 本地同步工具
 * 实现本地Vue文件与VTJ平台DSL的双向同步
 */
class VTJSyncTool {
  constructor(configPath = './vtj-config.json') {
    this.configPath = configPath;
    this.config = this.loadConfig();
    this.isWatching = false;
    this.syncQueue = new Set();

    // 确保本地目录存在
    fs.ensureDirSync(this.config.localDir);
    if (this.config.backupEnabled) {
      fs.ensureDirSync(this.config.backupDir);
    }
  }

  /**
   * 加载配置文件
   */
  loadConfig() {
    try {
      if (!fs.existsSync(this.configPath)) {
        console.log(chalk.yellow('⚠️  配置文件不存在，创建默认配置...'));
        this.createDefaultConfig();
      }

      const config = fs.readJsonSync(this.configPath);
      console.log(chalk.blue('📋 配置加载成功:'), {
        app: config.app,
        apiBase: config.apiBase,
        localDir: config.localDir
      });

      return config;
    } catch (error) {
      console.error(chalk.red('❌ 配置文件加载失败:'), error.message);
      process.exit(1);
    }
  }

  /**
   * 创建默认配置文件
   */
  createDefaultConfig() {
    const defaultConfig = {
      apiBase: "http://localhost:8080/api",
      app: "your-app-name",
      localDir: "./src/pages",
      platform: "web",
      autoSync: true,
      watchMode: true,
      syncInterval: 1000,
      backupEnabled: true,
      backupDir: "./backup",
      excludeFiles: ["*.backup", "*.tmp", "node_modules/**"],
      logLevel: "info"
    };

    fs.writeJsonSync(this.configPath, defaultConfig, { spaces: 2 });
    console.log(chalk.green('✅ 默认配置文件已创建，请修改配置后重新运行'));
  }

  /**
   * 记录日志
   */
  log(level, message, data = null) {
    const levels = { error: 0, warn: 1, info: 2, debug: 3 };
    const configLevel = levels[this.config.logLevel] || 2;

    if (levels[level] <= configLevel) {
      const timestamp = new Date().toLocaleTimeString();
      const colors = {
        error: chalk.red,
        warn: chalk.yellow,
        info: chalk.blue,
        debug: chalk.gray
      };

      console.log(`${colors[level](`[${timestamp}]`)} ${message}`);
      if (data) {
        console.log(data);
      }
    }
  }

  /**
   * 发送API请求
   */
  async apiRequest(method, url, data = null, params = {}) {
    try {
      const config = {
        method,
        url: `${this.config.apiBase}${url}`,
        headers: {
          'Content-Type': 'application/json'
        },
        params
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      this.log('error', `API请求失败: ${method} ${url}`, error.response?.data || error.message);
      throw error;
    }
  }

  /**
   * 获取页面列表
   */
  async getPageList() {
    try {
      const response = await this.apiRequest('GET', `/schemas/${this.config.app}/page`);
      return response.data || response.list || [];
    } catch (error) {
      this.log('error', '获取页面列表失败', error.message);
      return [];
    }
  }

  /**
   * 获取单个页面DSL
   */
  async getPageDSL(pageName) {
    try {
      const response = await this.apiRequest('GET', `/schemas/info/${this.config.app}/page`, null, { name: pageName });
      return response.data || response;
    } catch (error) {
      this.log('error', `获取页面DSL失败: ${pageName}`, error.message);
      return null;
    }
  }

  /**
   * DSL转Vue代码
   */
  async dslToVue(dsl) {
    try {
      const response = await this.apiRequest('POST', `/schemas/generator/${this.config.app}/vue`, dsl, {
        platform: this.config.platform
      });

      // 处理不同的响应格式
      if (typeof response === 'string') {
        return response;
      } else if (response.code) {
        return response.code;
      } else if (response.data) {
        return response.data;
      }

      return response;
    } catch (error) {
      this.log('error', 'DSL转Vue失败', error.message);
      throw error;
    }
  }

  /**
   * Vue代码转DSL (简化版本)
   */
  async vueToDSL(vueCode, pageName) {
    try {
      // 🔧 临时解决方案：直接创建包含Vue源码的DSL
      const template = this.extractTemplate(vueCode);
      const script = this.extractScript(vueCode);
      const style = this.extractStyle(vueCode);

      const dsl = {
        id: pageName,
        componentName: pageName,
        type: 'block',
        props: {},
        children: [{
          id: 'root',
          componentName: 'div',
          type: 'element',
          props: {
            innerHTML: template
          },
          children: []
        }],
        meta: {
          title: `${pageName} 页面`,
          description: '通过本地同步工具创建的页面',
          generated: false,
          timestamp: Date.now(),
          source: {
            template: template,
            script: script,
            style: style,
            full: vueCode
          }
        }
      };

      this.log('info', `Vue转DSL成功: ${pageName}`, `模板长度: ${template.length}, 脚本长度: ${script.length}, 样式长度: ${style.length}`);
      return dsl;

    } catch (error) {
      this.log('error', 'Vue转DSL失败', error.message);
      throw error;
    }
  }

  /**
   * 提取template部分
   */
  extractTemplate(vueContent) {
    const match = vueContent.match(/<template[^>]*>([\s\S]*?)<\/template>/);
    return match ? match[1].trim() : '';
  }

  /**
   * 提取script部分
   */
  extractScript(vueContent) {
    const match = vueContent.match(/<script[^>]*>([\s\S]*?)<\/script>/);
    return match ? match[1].trim() : '';
  }

  /**
   * 提取style部分
   */
  extractStyle(vueContent) {
    const match = vueContent.match(/<style[^>]*>([\s\S]*?)<\/style>/);
    return match ? match[1].trim() : '';
  }

  /**
   * 保存DSL到平台
   */
  async saveDSLToPlatform(pageName, dsl) {
    try {
      const requestData = {
        app: this.config.app,
        type: 'page',
        name: pageName,
        content: JSON.stringify(dsl)
      };

      this.log('info', `开始保存DSL到平台: ${pageName}`, `DSL类型: ${dsl.type}, 子组件数: ${dsl.children?.length || 0}`);

      const response = await this.apiRequest('POST', `/schemas/${this.config.app}/page`, requestData);

      this.log('success', `DSL保存成功: ${pageName}`, `响应: ${JSON.stringify(response).substring(0, 200)}...`);
      return response;
    } catch (error) {
      this.log('error', `保存DSL到平台失败: ${pageName}`, error.message);
      throw error;
    }
  }

  /**
   * 从平台拉取页面到本地
   */
  async pullPage(pageName) {
    const spinner = ora(`正在拉取页面: ${pageName}`).start();

    try {
      // 1. 获取页面DSL
      const pageData = await this.getPageDSL(pageName);
      if (!pageData || !pageData.content) {
        spinner.fail(`页面不存在或内容为空: ${pageName}`);
        return false;
      }

      // 2. DSL转Vue代码
      let dsl;
      try {
        dsl = typeof pageData.content === 'string' ? JSON.parse(pageData.content) : pageData.content;
      } catch (e) {
        dsl = pageData.content;
      }

      const vueCode = await this.dslToVue(dsl);

      // 3. 保存到本地文件
      const filePath = path.join(this.config.localDir, `${pageName}.vue`);

      // 备份现有文件
      if (this.config.backupEnabled && fs.existsSync(filePath)) {
        const backupPath = path.join(this.config.backupDir, `${pageName}_${Date.now()}.vue.backup`);
        fs.copySync(filePath, backupPath);
      }

      fs.writeFileSync(filePath, vueCode, 'utf8');

      spinner.succeed(`✅ 页面已同步到本地: ${filePath}`);
      this.log('info', `页面拉取成功: ${pageName} -> ${filePath}`);
      return true;

    } catch (error) {
      spinner.fail(`❌ 拉取页面失败: ${pageName}`);
      this.log('error', `拉取页面失败: ${pageName}`, error.message);
      return false;
    }
  }

  /**
   * 推送本地文件到平台
   */
  async pushPage(filePath) {
    const pageName = path.basename(filePath, '.vue');
    const spinner = ora(`正在推送页面: ${pageName}`).start();

    try {
      // 1. 读取Vue代码
      if (!fs.existsSync(filePath)) {
        spinner.fail(`文件不存在: ${filePath}`);
        return false;
      }

      const vueCode = fs.readFileSync(filePath, 'utf8');

      // 2. Vue代码转DSL
      const dsl = await this.vueToDSL(vueCode, pageName);

      // 3. 保存DSL到平台
      await this.saveDSLToPlatform(pageName, dsl);

      spinner.succeed(`✅ 页面已同步到平台: ${pageName}`);
      this.log('info', `页面推送成功: ${filePath} -> ${pageName}`);
      return true;

    } catch (error) {
      spinner.fail(`❌ 推送页面失败: ${pageName}`);
      this.log('error', `推送页面失败: ${filePath}`, error.message);
      return false;
    }
  }

  /**
   * 拉取所有页面到本地
   */
  async pullAllPages() {
    const spinner = ora('正在获取页面列表...').start();

    try {
      const pages = await this.getPageList();
      spinner.succeed(`找到 ${pages.length} 个页面`);

      if (pages.length === 0) {
        console.log(chalk.yellow('📝 没有找到任何页面'));
        return;
      }

      console.log(chalk.blue('📋 开始同步页面:'));
      let successCount = 0;

      for (const page of pages) {
        const pageName = page.name || page.id;
        const success = await this.pullPage(pageName);
        if (success) successCount++;
      }

      console.log(chalk.green(`🎉 同步完成! 成功: ${successCount}/${pages.length}`));

    } catch (error) {
      spinner.fail('获取页面列表失败');
      this.log('error', '拉取所有页面失败', error.message);
    }
  }

  /**
   * 推送所有本地文件到平台
   */
  async pushAllPages() {
    const spinner = ora('正在扫描本地文件...').start();

    try {
      const files = fs.readdirSync(this.config.localDir)
        .filter(file => file.endsWith('.vue'))
        .map(file => path.join(this.config.localDir, file));

      spinner.succeed(`找到 ${files.length} 个Vue文件`);

      if (files.length === 0) {
        console.log(chalk.yellow('📝 没有找到任何Vue文件'));
        return;
      }

      console.log(chalk.blue('📋 开始推送文件:'));
      let successCount = 0;

      for (const filePath of files) {
        const success = await this.pushPage(filePath);
        if (success) successCount++;
      }

      console.log(chalk.green(`🎉 推送完成! 成功: ${successCount}/${files.length}`));

    } catch (error) {
      spinner.fail('扫描本地文件失败');
      this.log('error', '推送所有页面失败', error.message);
    }
  }

  /**
   * 开始监听本地文件变化
   */
  startWatch() {
    if (this.isWatching) {
      console.log(chalk.yellow('⚠️  监听已经在运行中'));
      return;
    }

    console.log(chalk.blue(`👀 开始监听目录: ${this.config.localDir}`));
    console.log(chalk.gray('   按 Ctrl+C 停止监听'));

    const watcher = chokidar.watch(`${this.config.localDir}/*.vue`, {
      ignored: this.config.excludeFiles,
      persistent: true,
      ignoreInitial: true
    });

    // 防抖处理
    const debounceMap = new Map();

    const handleFileChange = (filePath) => {
      const fileName = path.basename(filePath);

      // 清除之前的定时器
      if (debounceMap.has(filePath)) {
        clearTimeout(debounceMap.get(filePath));
      }

      // 设置新的定时器
      const timer = setTimeout(async () => {
        console.log(chalk.cyan(`📝 检测到文件变化: ${fileName}`));
        await this.pushPage(filePath);
        debounceMap.delete(filePath);
      }, this.config.syncInterval);

      debounceMap.set(filePath, timer);
    };

    watcher
      .on('change', handleFileChange)
      .on('add', (filePath) => {
        console.log(chalk.green(`➕ 新文件: ${path.basename(filePath)}`));
        handleFileChange(filePath);
      })
      .on('unlink', (filePath) => {
        console.log(chalk.red(`🗑️  文件删除: ${path.basename(filePath)}`));
        // 可以在这里添加删除平台页面的逻辑
      })
      .on('error', (error) => {
        this.log('error', '文件监听错误', error);
      });

    this.isWatching = true;

    // 优雅退出处理
    process.on('SIGINT', () => {
      console.log(chalk.yellow('\n🛑 正在停止监听...'));
      watcher.close();
      process.exit(0);
    });
  }

  /**
   * 显示状态信息
   */
  async showStatus() {
    console.log(chalk.blue('📊 VTJ同步工具状态'));
    console.log(chalk.gray('─'.repeat(40)));

    // 配置信息
    console.log(chalk.cyan('配置信息:'));
    console.log(`  应用名称: ${this.config.app}`);
    console.log(`  API地址: ${this.config.apiBase}`);
    console.log(`  本地目录: ${this.config.localDir}`);
    console.log(`  平台: ${this.config.platform}`);

    // 本地文件统计
    try {
      const localFiles = fs.readdirSync(this.config.localDir)
        .filter(file => file.endsWith('.vue'));
      console.log(chalk.cyan(`\n本地文件: ${localFiles.length} 个`));
      localFiles.forEach(file => {
        console.log(`  📄 ${file}`);
      });
    } catch (error) {
      console.log(chalk.red('❌ 无法读取本地目录'));
    }

    // 平台页面统计
    try {
      const pages = await this.getPageList();
      console.log(chalk.cyan(`\n平台页面: ${pages.length} 个`));
      pages.forEach(page => {
        const name = page.name || page.id;
        console.log(`  🌐 ${name}`);
      });
    } catch (error) {
      console.log(chalk.red('❌ 无法获取平台页面列表'));
    }
  }
}

module.exports = VTJSyncTool;
