#!/usr/bin/env node

const { Command } = require('commander');
const inquirer = require('inquirer');
const chalk = require('chalk');
const VTJSyncTool = require('./vtj-sync');

const program = new Command();

// 显示欢迎信息
console.log(chalk.blue.bold('🚀 VTJ 本地同步工具'));
console.log(chalk.gray('   实现本地Vue文件与VTJ平台DSL的双向同步\n'));

program
  .name('vtj-sync')
  .description('VTJ低代码平台本地同步工具')
  .version('1.0.0');

// 初始化配置
program
  .command('init')
  .description('初始化配置文件')
  .action(async () => {
    console.log(chalk.blue('🔧 初始化VTJ同步工具配置\n'));
    
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'apiBase',
        message: 'VTJ API地址:',
        default: 'http://localhost:8080/api'
      },
      {
        type: 'input',
        name: 'app',
        message: '应用名称:',
        validate: (input) => input.trim() !== '' || '应用名称不能为空'
      },
      {
        type: 'input',
        name: 'localDir',
        message: '本地目录:',
        default: './src/pages'
      },
      {
        type: 'list',
        name: 'platform',
        message: '目标平台:',
        choices: ['web', 'h5', 'uniapp'],
        default: 'web'
      },
      {
        type: 'confirm',
        name: 'autoSync',
        message: '启用自动同步?',
        default: true
      }
    ]);
    
    const config = {
      ...answers,
      watchMode: true,
      syncInterval: 1000,
      backupEnabled: true,
      backupDir: './backup',
      excludeFiles: ['*.backup', '*.tmp', 'node_modules/**'],
      logLevel: 'info'
    };
    
    const fs = require('fs-extra');
    fs.writeJsonSync('./vtj-config.json', config, { spaces: 2 });
    
    console.log(chalk.green('\n✅ 配置文件已创建: vtj-config.json'));
    console.log(chalk.blue('💡 现在可以使用以下命令:'));
    console.log('   vtj-sync pull    # 拉取所有页面到本地');
    console.log('   vtj-sync watch   # 开始监听文件变化');
    console.log('   vtj-sync status  # 查看状态信息');
  });

// 拉取页面
program
  .command('pull [page]')
  .description('从平台拉取页面到本地')
  .option('-a, --all', '拉取所有页面')
  .action(async (page, options) => {
    const syncTool = new VTJSyncTool();
    
    if (options.all || !page) {
      await syncTool.pullAllPages();
    } else {
      await syncTool.pullPage(page);
    }
  });

// 推送页面
program
  .command('push [page]')
  .description('推送本地页面到平台')
  .option('-a, --all', '推送所有页面')
  .action(async (page, options) => {
    const syncTool = new VTJSyncTool();
    
    if (options.all || !page) {
      await syncTool.pushAllPages();
    } else {
      const filePath = page.endsWith('.vue') ? page : `${syncTool.config.localDir}/${page}.vue`;
      await syncTool.pushPage(filePath);
    }
  });

// 监听文件变化
program
  .command('watch')
  .description('监听本地文件变化并自动同步')
  .action(() => {
    const syncTool = new VTJSyncTool();
    syncTool.startWatch();
  });

// 显示状态
program
  .command('status')
  .description('显示同步状态信息')
  .action(async () => {
    const syncTool = new VTJSyncTool();
    await syncTool.showStatus();
  });

// 交互式菜单
program
  .command('menu')
  .description('显示交互式菜单')
  .action(async () => {
    const syncTool = new VTJSyncTool();
    
    while (true) {
      console.log('\n');
      const { action } = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: '请选择操作:',
          choices: [
            { name: '📥 拉取所有页面到本地', value: 'pullAll' },
            { name: '📤 推送所有页面到平台', value: 'pushAll' },
            { name: '👀 开始监听文件变化', value: 'watch' },
            { name: '📊 查看状态信息', value: 'status' },
            { name: '🚪 退出', value: 'exit' }
          ]
        }
      ]);
      
      switch (action) {
        case 'pullAll':
          await syncTool.pullAllPages();
          break;
        case 'pushAll':
          await syncTool.pushAllPages();
          break;
        case 'watch':
          syncTool.startWatch();
          return; // 监听模式会阻塞，直接返回
        case 'status':
          await syncTool.showStatus();
          break;
        case 'exit':
          console.log(chalk.blue('👋 再见!'));
          process.exit(0);
      }
      
      // 询问是否继续
      const { continue: shouldContinue } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'continue',
          message: '继续使用?',
          default: true
        }
      ]);
      
      if (!shouldContinue) {
        console.log(chalk.blue('👋 再见!'));
        break;
      }
    }
  });

// 如果没有参数，显示帮助信息
if (process.argv.length === 2) {
  program.help();
}

program.parse();
