package com.vtj.backend.service;

import com.vtj.backend.dto.ParseVueDto;

import java.util.Map;

/**
 * 代码转换服务接口
 */
public interface CodeConverterService {
    
    /**
     * DSL 转 Vue 代码
     * @param app 应用ID
     * @param platform 平台类型
     * @param dsl DSL数据
     * @param componentMap 组件映射
     * @param dependencies 依赖列表
     * @return Vue代码
     */
    String generateVue(String app, String platform, Map<String, Object> dsl, 
                      Map<String, Object> componentMap, Object dependencies);
    
    /**
     * Vue 代码转 DSL
     * @param dto 解析参数
     * @return DSL数据
     */
    Map<String, Object> parseVue(ParseVueDto dto);
    
    /**
     * 检查代码转换服务是否可用
     * @return 是否可用
     */
    boolean isServiceAvailable();
}
