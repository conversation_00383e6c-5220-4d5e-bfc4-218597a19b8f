package com.vtj.backend.service.impl;

import com.alibaba.fastjson.JSON;
import com.vtj.backend.dto.ParseVueDto;
import com.vtj.backend.dto.SchemaDto;
import com.vtj.backend.entity.Schema;
import com.vtj.backend.repository.SchemaRepository;
import com.vtj.backend.service.SchemaService;
import com.vtj.backend.service.CodeConverterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Objects;

/**
 * Schema 服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchemaServiceImpl implements SchemaService {

    private final SchemaRepository schemaRepository;
    private final CodeConverterService codeConverterService;

    @Override
    @Transactional
    public Map<String, Object> save(SchemaDto dto) {
        // 查找是否已存在
        Optional<Schema> existingOpt = schemaRepository.findByAppAndTypeAndName(
                dto.getApp(), dto.getType(), dto.getName());

        Schema schema;
        if (existingOpt.isPresent()) {
            // 更新现有记录
            schema = existingOpt.get();
            schema.setContent(dto.getContent());
            schema.setDescription(dto.getDescription());
            schema.setVersion(schema.getVersion() + 1);
            log.info("更新 Schema: {}/{}/{}", dto.getApp(), dto.getType(), dto.getName());
        } else {
            // 创建新记录
            schema = new Schema();
            schema.setApp(dto.getApp());
            schema.setType(dto.getType());
            schema.setName(dto.getName());
            schema.setContent(dto.getContent());
            schema.setDescription(dto.getDescription());
            schema.setVersion(1);
            schema.setUserId(dto.getUserId());
            log.info("创建 Schema: {}/{}/{}", dto.getApp(), dto.getType(), dto.getName());
        }

        schema = schemaRepository.save(schema);
        
        // 🔧 强制刷新到数据库，确保立即可查询
        schemaRepository.flush();
        
        return entityToMap(schema);
    }

    @Override
    @Transactional
    public Map<String, Object> findOne(String app, String type, String name) {
        log.debug("查找Schema: app={}, type={}, name={}", app, type, name);

        try {
            Optional<Schema> schemaOpt = schemaRepository.findByAppAndTypeAndName(app, type, name);
            if (schemaOpt.isPresent()) {
                Schema schema = schemaOpt.get();
                Map<String, Object> result = new HashMap<>();
                result.put("id", schema.getId().toString());
                result.put("app", schema.getApp());
                result.put("name", schema.getName());
                result.put("type", schema.getType());

                // 解析并返回 DSL 内容
                if (schema.getContent() != null) {
                    try {
                        result.put("content", JSON.parseObject(schema.getContent()));
                    } catch (Exception e) {
                        log.warn("解析 Schema 内容失败: {}", e.getMessage());
                        result.put("content", new HashMap<>());
                    }
                } else {
                    result.put("content", new HashMap<>());
                }

                log.debug("找到Schema: {}", result);
                return result;
            }

            // 如果没找到，尝试创建默认Schema
            log.info("Schema不存在，尝试创建默认Schema: app={}, type={}, name={}", app, type, name);

            // 为特定类型创建默认Schema
            if ("project".equals(type) && app.equals(name)) {
                return createDefaultProjectSchema(app);
            } else if ("material".equals(type) && app.equals(name)) {
                return createDefaultMaterialSchema(app);
            }

            // 返回空的结果但不抛出异常
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("app", app);
            emptyResult.put("type", type);
            emptyResult.put("name", name);
            emptyResult.put("content", new HashMap<>());
            log.info("返回空Schema结构: app={}, type={}, name={}", app, type, name);
            return emptyResult;

        } catch (Exception e) {
            log.error("查找Schema时发生异常: app={}, type={}, name={}", app, type, name, e);

            // 即使发生异常，也返回空结构而不是抛出异常
            Map<String, Object> emptyResult = new HashMap<>();
            emptyResult.put("app", app);
            emptyResult.put("type", type);
            emptyResult.put("name", name);
            emptyResult.put("content", new HashMap<>());
            return emptyResult;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> find(String app, String type, String name) {
        List<Schema> schemas;
        if (name != null && !name.isEmpty()) {
            schemas = schemaRepository.findByAppAndTypeAndNameLike(app, type, name);
        } else {
            schemas = schemaRepository.findByAppAndType(app, type);
        }

        return schemas.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> search(String app, String type, String name, String keyword) {
        List<Schema> schemas = schemaRepository.searchSchemas(app, type, name, keyword);
        return schemas.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> findOneById(String id) {
        try {
            Long schemaId = Long.parseLong(id);
            Optional<Schema> schemaOpt = schemaRepository.findById(schemaId);
            if (schemaOpt.isPresent()) {
                Schema schema = schemaOpt.get();
                Map<String, Object> result = new HashMap<>();
                result.put("id", schema.getId().toString());
                result.put("app", schema.getApp());
                result.put("name", schema.getName());
                result.put("type", schema.getType());
                result.put("version", schema.getVersion());
                result.put("description", schema.getDescription());
                result.put("createdAt", schema.getCreatedAt());
                result.put("updatedAt", schema.getUpdatedAt());

                // 解析并返回 DSL 内容
                if (schema.getContent() != null) {
                    try {
                        result.put("content", JSON.parseObject(schema.getContent()));
                    } catch (Exception e) {
                        log.warn("解析 Schema 内容失败: {}", e.getMessage());
                        result.put("content", new HashMap<>());
                    }
                } else {
                    result.put("content", new HashMap<>());
                }

                return result;
            }
        } catch (NumberFormatException e) {
            log.warn("无效的Schema ID: {}", id);
        }

        // 如果没有找到，返回空的 Schema 结构
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("app", "");
        result.put("name", "");
        result.put("type", "");
        result.put("content", new HashMap<>());
        return result;
    }

    @Override
    @Transactional
    public void remove(String app, String type, List<String> names) {
        schemaRepository.deleteByAppAndTypeAndNameIn(app, type, names);
        log.info("删除 Schema: {}/{} - {}", app, type, names);
    }

    @Override
    @Transactional
    public void removeByIds(List<String> ids) {
        List<Long> longIds = ids.stream()
                .map(id -> {
                    try {
                        return Long.parseLong(id);
                    } catch (NumberFormatException e) {
                        log.warn("无效的Schema ID: {}", id);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

        if (!longIds.isEmpty()) {
            schemaRepository.deleteByIdIn(longIds);
            log.info("根据ID批量删除 Schema: {}", longIds);
        }
    }

    @Override
    public String generateVue(String app, String platform, Map<String, Object> dsl) {
        log.info("开始 DSL 转 Vue: app={}, platform={}", app, platform);

        try {
            // 获取组件映射和依赖信息
            Map<String, Object> componentMap = getMaterialsForApp(app);
            Object dependencies = getDependenciesForApp(app);

            // 调用代码转换服务
            return codeConverterService.generateVue(app, platform, dsl, componentMap, dependencies);

        } catch (Exception e) {
            log.error("DSL 转 Vue 失败: app={}, platform={}", app, platform, e);
            throw new RuntimeException("DSL 转 Vue 失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> parseVue(ParseVueDto dto) {
        log.info("开始 Vue 转 DSL: id={}, name={}", dto.getId(), dto.getName());

        try {
            // 调用代码转换服务
            return codeConverterService.parseVue(dto);

        } catch (Exception e) {
            log.error("Vue 转 DSL 失败: id={}, name={}", dto.getId(), dto.getName(), e);
            throw new RuntimeException("Vue 转 DSL 失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> generateProject(String app, Map<String, Object> project) {
        log.info("开始项目出码: app={}", app);

        try {
            // 1. 获取项目所有页面和组件
            List<Schema> schemas = schemaRepository.findByAppAndType(app, "page");
            List<Schema> components = schemaRepository.findByAppAndType(app, "component");

            log.info("找到页面数量: {}, 组件数量: {}", schemas.size(), components.size());

            // 2. 生成项目结构
            Map<String, String> projectFiles = new HashMap<>();

            // 生成package.json
            projectFiles.put("package.json", generatePackageJson(project));

            // 生成主入口文件
            projectFiles.put("src/main.js", generateMainJs());
            projectFiles.put("src/App.vue", generateAppVue());

            // 生成路由文件
            projectFiles.put("src/router/index.js", generateRouter(schemas));

            // 生成页面文件
            for (Schema schema : schemas) {
                String vueCode = generateVueFromSchema(schema);
                projectFiles.put("src/views/" + schema.getName() + ".vue", vueCode);
            }

            // 生成组件文件
            for (Schema component : components) {
                String vueCode = generateVueFromSchema(component);
                projectFiles.put("src/components/" + component.getName() + ".vue", vueCode);
            }

            // 生成配置文件
            projectFiles.put("vite.config.js", generateViteConfig());
            projectFiles.put("index.html", generateIndexHtml(project));

            // 3. 创建ZIP文件 (这里简化处理，实际应该生成真实的ZIP文件)
            String projectName = (String) project.getOrDefault("name", app);
            String downloadUrl = createProjectZip(projectName, projectFiles);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "项目出码成功");
            result.put("downloadUrl", downloadUrl);
            result.put("fileCount", projectFiles.size());
            result.put("projectName", projectName);

            log.info("项目出码完成: {} 个文件", projectFiles.size());
            return result;

        } catch (Exception e) {
            log.error("项目出码失败: app={}", app, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "项目出码失败");
            return result;
        }
    }

    /**
     * 实体转换为 Map
     */
    private Map<String, Object> entityToMap(Schema schema) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", schema.getId());
        map.put("app", schema.getApp());
        map.put("type", schema.getType());
        map.put("name", schema.getName());
        map.put("version", schema.getVersion());
        map.put("description", schema.getDescription());
        map.put("createdAt", schema.getCreatedAt());
        map.put("updatedAt", schema.getUpdatedAt());
        
        // 如果需要返回内容，解析 JSON
        if (schema.getContent() != null) {
            try {
                map.put("content", JSON.parseObject(schema.getContent()));
            } catch (Exception e) {
                map.put("content", schema.getContent());
            }
        }

        return map;
    }

    /**
     * 获取应用的物料信息
     */
    private Map<String, Object> getMaterialsForApp(String app) {
        try {
            Optional<Schema> materialOpt = schemaRepository.findByAppAndTypeAndName(app, "material", app);
            if (materialOpt.isPresent() && materialOpt.get().getContent() != null) {
                return JSON.parseObject(materialOpt.get().getContent());
            }
        } catch (Exception e) {
            log.warn("获取应用物料信息失败: app={}", app, e);
        }
        return new HashMap<>();
    }

    /**
     * 获取应用的依赖信息
     */
    private Object getDependenciesForApp(String app) {
        try {
            Optional<Schema> projectOpt = schemaRepository.findByAppAndTypeAndName(app, "project", app);
            if (projectOpt.isPresent() && projectOpt.get().getContent() != null) {
                Map<String, Object> project = JSON.parseObject(projectOpt.get().getContent());
                return project.get("dependencies");
            }
        } catch (Exception e) {
            log.warn("获取应用依赖信息失败: app={}", app, e);
        }
        return new ArrayList<>();
    }

    /**
     * 生成package.json文件
     */
    private String generatePackageJson(Map<String, Object> project) {
        String projectName = (String) project.getOrDefault("name", "vtj-project");
        String version = (String) project.getOrDefault("version", "1.0.0");
        String description = (String) project.getOrDefault("description", "VTJ低代码平台生成的项目");

        return String.format("""
            {
              "name": "%s",
              "version": "%s",
              "description": "%s",
              "type": "module",
              "scripts": {
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview"
              },
              "dependencies": {
                "vue": "^3.3.0",
                "vue-router": "^4.2.0",
                "@vtj/ui": "latest"
              },
              "devDependencies": {
                "@vitejs/plugin-vue": "^4.2.0",
                "vite": "^4.3.0"
              }
            }
            """, projectName, version, description);
    }

    /**
     * 生成main.js文件
     */
    private String generateMainJs() {
        return """
            import { createApp } from 'vue'
            import App from './App.vue'
            import router from './router'

            const app = createApp(App)
            app.use(router)
            app.mount('#app')
            """;
    }

    /**
     * 生成App.vue文件
     */
    private String generateAppVue() {
        return """
            <template>
              <div id="app">
                <router-view />
              </div>
            </template>

            <script>
            export default {
              name: 'App'
            }
            </script>

            <style>
            #app {
              font-family: Avenir, Helvetica, Arial, sans-serif;
              -webkit-font-smoothing: antialiased;
              -moz-osx-font-smoothing: grayscale;
            }
            </style>
            """;
    }

    /**
     * 生成路由文件
     */
    private String generateRouter(List<Schema> schemas) {
        StringBuilder routes = new StringBuilder();
        routes.append("import { createRouter, createWebHistory } from 'vue-router'\n\n");

        // 导入页面组件
        for (Schema schema : schemas) {
            routes.append(String.format("import %s from '../views/%s.vue'\n",
                schema.getName(), schema.getName()));
        }

        routes.append("\nconst routes = [\n");

        // 生成路由配置
        for (int i = 0; i < schemas.size(); i++) {
            Schema schema = schemas.get(i);
            String path = i == 0 ? "/" : "/" + schema.getName().toLowerCase();
            routes.append(String.format("  { path: '%s', name: '%s', component: %s }%s\n",
                path, schema.getName(), schema.getName(),
                i < schemas.size() - 1 ? "," : ""));
        }

        routes.append("]\n\n");
        routes.append("const router = createRouter({\n");
        routes.append("  history: createWebHistory(),\n");
        routes.append("  routes\n");
        routes.append("})\n\n");
        routes.append("export default router\n");

        return routes.toString();
    }

    /**
     * 从Schema生成Vue代码
     */
    private String generateVueFromSchema(Schema schema) {
        try {
            // 使用现有的generateVue方法
            String platform = "web";
            Map<String, Object> dsl = JSON.parseObject(schema.getContent());
            return generateVue(schema.getApp(), platform, dsl);
        } catch (Exception e) {
            log.warn("生成Vue代码失败，使用默认模板: {}", e.getMessage());
            return generateDefaultVue(schema.getName());
        }
    }

    /**
     * 生成默认Vue组件
     */
    private String generateDefaultVue(String name) {
        return String.format("""
            <template>
              <div class="%s">
                <h1>%s</h1>
                <p>这是由VTJ低代码平台生成的页面</p>
              </div>
            </template>

            <script>
            export default {
              name: '%s'
            }
            </script>

            <style scoped>
            .%s {
              padding: 20px;
            }
            </style>
            """, name.toLowerCase(), name, name, name.toLowerCase());
    }

    /**
     * 生成Vite配置文件
     */
    private String generateViteConfig() {
        return """
            import { defineConfig } from 'vite'
            import vue from '@vitejs/plugin-vue'

            export default defineConfig({
              plugins: [vue()],
              server: {
                port: 3000,
                open: true
              },
              build: {
                outDir: 'dist',
                assetsDir: 'assets'
              }
            })
            """;
    }

    /**
     * 生成index.html文件
     */
    private String generateIndexHtml(Map<String, Object> project) {
        String title = (String) project.getOrDefault("title", "VTJ项目");
        return String.format("""
            <!DOCTYPE html>
            <html lang="zh-CN">
              <head>
                <meta charset="UTF-8" />
                <link rel="icon" type="image/svg+xml" href="/vite.svg" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <title>%s</title>
              </head>
              <body>
                <div id="app"></div>
                <script type="module" src="/src/main.js"></script>
              </body>
            </html>
            """, title);
    }

    /**
     * 创建项目ZIP文件
     */
    private String createProjectZip(String projectName, Map<String, String> projectFiles) {
        // 这里简化处理，实际应该创建真实的ZIP文件并上传到OSS
        // 返回模拟的下载链接
        String timestamp = String.valueOf(System.currentTimeMillis());
        return String.format("http://example.com/download/%s-%s.zip", projectName, timestamp);
    }

    /**
     * 创建默认项目Schema
     */
    private Map<String, Object> createDefaultProjectSchema(String app) {
        log.info("创建默认项目Schema: {}", app);

        Map<String, Object> projectConfig = new HashMap<>();
        projectConfig.put("id", app);
        projectConfig.put("name", app);
        projectConfig.put("title", app + "项目");
        projectConfig.put("description", "VTJ低代码平台生成的项目");
        projectConfig.put("platform", "web");
        projectConfig.put("version", "1.0.0");
        projectConfig.put("pages", new ArrayList<>());
        projectConfig.put("blocks", new ArrayList<>());
        projectConfig.put("dependencies", new ArrayList<>());

        // 保存到数据库
        try {
            Schema schema = new Schema();
            schema.setApp(app);
            schema.setType("project");
            schema.setName(app);
            schema.setContent(JSON.toJSONString(projectConfig));
            schema.setDescription("默认项目配置");
            schema = schemaRepository.save(schema);

            Map<String, Object> result = new HashMap<>();
            result.put("id", schema.getId().toString());
            result.put("app", app);
            result.put("type", "project");
            result.put("name", app);
            result.put("content", projectConfig);

            log.info("默认项目Schema创建成功: {}", app);
            return result;
        } catch (Exception e) {
            log.error("创建默认项目Schema失败: {}", app, e);
            Map<String, Object> result = new HashMap<>();
            result.put("app", app);
            result.put("type", "project");
            result.put("name", app);
            result.put("content", projectConfig);
            return result;
        }
    }

    /**
     * 创建默认物料Schema
     */
    private Map<String, Object> createDefaultMaterialSchema(String app) {
        log.info("创建默认物料Schema: {}", app);

        Map<String, Object> materialConfig = new HashMap<>();
        materialConfig.put("components", new HashMap<>());
        materialConfig.put("blocks", new HashMap<>());
        materialConfig.put("templates", new HashMap<>());

        // 添加一些基础组件
        Map<String, Object> basicComponents = new HashMap<>();
        basicComponents.put("div", Map.of(
            "name", "div",
            "title", "容器",
            "category", "基础",
            "props", new HashMap<>()
        ));
        basicComponents.put("text", Map.of(
            "name", "text",
            "title", "文本",
            "category", "基础",
            "props", Map.of("content", "文本内容")
        ));
        materialConfig.put("components", basicComponents);

        // 保存到数据库
        try {
            Schema schema = new Schema();
            schema.setApp(app);
            schema.setType("material");
            schema.setName(app);
            schema.setContent(JSON.toJSONString(materialConfig));
            schema.setDescription("默认物料配置");
            schema = schemaRepository.save(schema);

            Map<String, Object> result = new HashMap<>();
            result.put("id", schema.getId().toString());
            result.put("app", app);
            result.put("type", "material");
            result.put("name", app);
            result.put("content", materialConfig);

            log.info("默认物料Schema创建成功: {}", app);
            return result;
        } catch (Exception e) {
            log.error("创建默认物料Schema失败: {}", app, e);
            Map<String, Object> result = new HashMap<>();
            result.put("app", app);
            result.put("type", "material");
            result.put("name", app);
            result.put("content", materialConfig);
            return result;
        }
    }
}