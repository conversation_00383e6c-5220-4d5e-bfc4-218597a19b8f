#!/bin/bash

echo "========================================"
echo "    VTJ 后端 API 功能测试"
echo "========================================"

BASE_URL="http://localhost:8080/api"
CODER_URL="http://localhost:3001"

echo ""
echo "[1/6] 测试代码转换服务..."

# 测试代码转换服务健康检查
echo "🔍 检查代码转换服务健康状态..."
curl -s "$CODER_URL/health" | jq '.' || echo "❌ 代码转换服务不可用"

echo ""
echo "[2/6] 测试 Schema 保存..."

# 测试保存 Schema
echo "💾 保存测试 Schema..."
curl -X POST "$BASE_URL/schemas/test-app/project" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-project",
    "content": "{\"id\":\"test-project\",\"name\":\"测试项目\",\"platform\":\"web\"}",
    "description": "测试项目"
  }' | jq '.' || echo "❌ Schema 保存失败"

echo ""
echo "[3/6] 测试 Schema 查询..."

# 测试查询 Schema
echo "🔍 查询测试 Schema..."
curl -s "$BASE_URL/schemas/info/test-app/project?name=test-project" | jq '.' || echo "❌ Schema 查询失败"

echo ""
echo "[4/6] 测试 DSL 转 Vue..."

# 测试 DSL 转 Vue
echo "🔄 测试 DSL 转 Vue 转换..."
curl -X POST "$BASE_URL/schemas/generator/test-app/vue?platform=web" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-component",
    "componentName": "TestComponent",
    "type": "block",
    "props": {},
    "children": [
      {
        "componentName": "div",
        "props": {
          "style": {
            "padding": "20px"
          }
        },
        "children": [
          {
            "componentName": "h1",
            "children": ["Hello VTJ!"]
          }
        ]
      }
    ]
  }' || echo "❌ DSL 转 Vue 失败"

echo ""
echo "[5/6] 测试 Vue 转 DSL..."

# 测试 Vue 转 DSL
echo "🔄 测试 Vue 转 DSL 转换..."
curl -X POST "$BASE_URL/schemas/parser" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-vue",
    "name": "TestVue",
    "source": "<template><div><h1>Hello World</h1></div></template>",
    "project": "{\"id\":\"test\",\"platform\":\"web\"}"
  }' | jq '.' || echo "❌ Vue 转 DSL 失败"

echo ""
echo "[6/6] 测试项目出码..."

# 测试项目出码
echo "📦 测试项目出码..."
curl -X POST "$BASE_URL/schemas/generator/test-app/project" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-project",
    "name": "测试项目",
    "platform": "web",
    "pages": [
      {
        "id": "home",
        "name": "首页",
        "path": "/home"
      }
    ],
    "blocks": []
  }' | jq '.' || echo "❌ 项目出码失败"

echo ""
echo "========================================"
echo "    测试完成！"
echo "========================================"
echo ""
echo "💡 提示："
echo "   - 如果看到 JSON 输出，说明接口正常"
echo "   - 如果看到错误信息，请检查服务是否正常启动"
echo "   - 确保两个服务都在运行："
echo "     * Spring Boot: http://localhost:8080"
echo "     * Node.js: http://localhost:3001"
echo ""
