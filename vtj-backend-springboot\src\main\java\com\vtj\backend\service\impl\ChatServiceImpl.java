package com.vtj.backend.service.impl;

import com.vtj.backend.dto.UserChatDto;
import com.vtj.backend.entity.Chat;
import com.vtj.backend.repository.ChatRepository;
import com.vtj.backend.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * AI对话服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements ChatService {

    private final ChatRepository chatRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getChats(String topicId) {
        List<Chat> chats = chatRepository.findByTopicIdOrderByCreatedAtAsc(topicId);
        return chats.stream()
                .map(this::entityToMap)
                .toList();
    }

    @Override
    @Transactional
    public String postChat(UserChatDto dto) {
        Chat chat = new Chat();
        chat.setChatId("chat-" + System.currentTimeMillis());
        chat.setTopicId(dto.getTopicId());
        chat.setRole(dto.getRole() != null ? dto.getRole() : "user");
        chat.setContent(dto.getContent());
        chat.setTokens(dto.getTokens() != null ? dto.getTokens() : 0);
        chat.setStatus(1);

        chat = chatRepository.save(chat);
        log.info("创建对话: {} - {}", chat.getChatId(), chat.getRole());

        return chat.getChatId();
    }

    @Override
    @Transactional
    public void saveChat(String chatId, String topicId, String role, String content, Integer tokens) {
        Optional<Chat> chatOpt = chatRepository.findByChatId(chatId);
        
        Chat chat;
        if (chatOpt.isPresent()) {
            chat = chatOpt.get();
            chat.setContent(content);
            chat.setTokens(tokens != null ? tokens : 0);
        } else {
            chat = new Chat();
            chat.setChatId(chatId);
            chat.setTopicId(topicId);
            chat.setRole(role);
            chat.setContent(content);
            chat.setTokens(tokens != null ? tokens : 0);
            chat.setStatus(1);
        }

        chatRepository.save(chat);
        log.info("保存对话: {} - {}", chatId, role);
    }

    @Override
    @Transactional
    public void deleteChatsByTopicId(String topicId) {
        chatRepository.deleteByTopicId(topicId);
        log.info("删除话题对话: {}", topicId);
    }

    /**
     * 实体转Map
     */
    private Map<String, Object> entityToMap(Chat chat) {
        Map<String, Object> map = new HashMap<>();
        map.put("chatId", chat.getChatId());
        map.put("topicId", chat.getTopicId());
        map.put("role", chat.getRole());
        map.put("content", chat.getContent());
        map.put("tokens", chat.getTokens());
        map.put("status", chat.getStatus());
        map.put("createdAt", chat.getCreatedAt());
        map.put("updatedAt", chat.getUpdatedAt());
        return map;
    }
}
