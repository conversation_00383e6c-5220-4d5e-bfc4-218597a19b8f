#!/bin/bash

# VTJ同步工具安装脚本

echo "🚀 VTJ同步工具安装脚本"
echo "=========================="

# 检查Node.js版本
echo "📋 检查环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (>= 16.0.0)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Node.js 版本过低，当前版本: $NODE_VERSION，需要版本: >= $REQUIRED_VERSION"
    exit 1
fi

echo "✅ Node.js 版本检查通过: $NODE_VERSION"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ npm 检查通过"

# 安装依赖
echo ""
echo "📦 安装依赖包..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装成功"

# 创建可执行文件链接
echo ""
echo "🔗 创建可执行文件..."
chmod +x cli.js
chmod +x vtj-sync.js
chmod +x example.js

# 检查是否已存在配置文件
if [ ! -f "vtj-config.json" ]; then
    echo ""
    echo "⚙️  初始化配置文件..."
    
    # 提示用户输入配置
    echo "请输入VTJ配置信息："
    
    read -p "VTJ API地址 (默认: http://localhost:8080/api): " API_BASE
    API_BASE=${API_BASE:-"http://localhost:8080/api"}
    
    read -p "应用名称: " APP_NAME
    while [ -z "$APP_NAME" ]; do
        echo "应用名称不能为空"
        read -p "应用名称: " APP_NAME
    done
    
    read -p "本地目录 (默认: ./src/pages): " LOCAL_DIR
    LOCAL_DIR=${LOCAL_DIR:-"./src/pages"}
    
    read -p "目标平台 (web/h5/uniapp, 默认: web): " PLATFORM
    PLATFORM=${PLATFORM:-"web"}
    
    # 创建配置文件
    cat > vtj-config.json << EOF
{
  "apiBase": "$API_BASE",
  "app": "$APP_NAME",
  "localDir": "$LOCAL_DIR",
  "platform": "$PLATFORM",
  "autoSync": true,
  "watchMode": true,
  "syncInterval": 1000,
  "backupEnabled": true,
  "backupDir": "./backup",
  "excludeFiles": [
    "*.backup",
    "*.tmp",
    "node_modules/**"
  ],
  "logLevel": "info"
}
EOF
    
    echo "✅ 配置文件已创建"
else
    echo "⚠️  配置文件已存在，跳过初始化"
fi

# 创建本地目录
echo ""
echo "📁 创建本地目录..."
mkdir -p src/pages
mkdir -p backup

echo "✅ 目录创建完成"

# 测试连接
echo ""
echo "🔍 测试VTJ后端连接..."
if command -v curl &> /dev/null; then
    if curl -s "$API_BASE/open/auth/test" > /dev/null; then
        echo "✅ VTJ后端连接成功"
    else
        echo "⚠️  VTJ后端连接失败，请确保后端服务正在运行"
    fi
else
    echo "⚠️  curl 未安装，跳过连接测试"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 可用命令："
echo "  node cli.js init     # 重新初始化配置"
echo "  node cli.js status   # 查看状态"
echo "  node cli.js pull     # 拉取所有页面"
echo "  node cli.js watch    # 开始监听"
echo "  node cli.js menu     # 交互式菜单"
echo ""
echo "📖 更多信息请查看 README.md"
echo ""
echo "🚀 现在可以开始使用VTJ同步工具了！"
