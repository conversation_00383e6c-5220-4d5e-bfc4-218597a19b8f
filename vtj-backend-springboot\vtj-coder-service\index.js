import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { generator } from '@vtj/coder';
import { parseVue } from '@vtj/parser';

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'vtj-coder-service',
    timestamp: new Date().toISOString()
  });
});

/**
 * DSL 转 Vue 代码
 * POST /api/generator/vue
 */
app.post('/api/generator/vue', async (req, res) => {
  try {
    const { dsl, componentMap, dependencies, platform, formatterDisabled } = req.body;
    
    console.log('收到 DSL 转 Vue 请求:', {
      platform: platform || 'web',
      hasComponentMap: !!componentMap,
      hasDependencies: !!dependencies,
      formatterDisabled: formatterDisabled || false
    });

    // 转换 componentMap 为 Map 对象
    let componentMapObj = null;
    if (componentMap) {
      componentMapObj = new Map(Object.entries(componentMap));
    }

    // 调用 @vtj/coder 进行转换
    const vueCode = await generator(
      dsl,
      componentMapObj,
      dependencies,
      platform || 'web',
      formatterDisabled || false
    );

    res.json({
      success: true,
      code: vueCode,
      message: 'DSL 转 Vue 成功'
    });

  } catch (error) {
    console.error('DSL 转 Vue 失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'DSL 转 Vue 失败'
    });
  }
});

/**
 * Vue 代码转 DSL
 * POST /api/parser/vue
 */
app.post('/api/parser/vue', async (req, res) => {
  try {
    const options = req.body;
    
    console.log('收到 Vue 转 DSL 请求:', {
      hasSource: !!options.source,
      id: options.id,
      name: options.name
    });

    // 调用 @vtj/parser 进行转换
    const dsl = await parseVue(options);

    res.json({
      success: true,
      dsl: dsl,
      message: 'Vue 转 DSL 成功'
    });

  } catch (error) {
    console.error('Vue 转 DSL 失败:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Vue 转 DSL 失败'
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    error: error.message,
    message: '服务器内部错误'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 VTJ 代码转换服务启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📝 DSL转Vue: POST http://localhost:${PORT}/api/generator/vue`);
  console.log(`🔄 Vue转DSL: POST http://localhost:${PORT}/api/parser/vue`);
});
