package com.vtj.backend.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模板DSL版本记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "template_dsl")
public class TemplateDsl extends BaseEntity {

    /**
     * 模板ID
     */
    @Column(name = "template_id", nullable = false, length = 50)
    private String templateId;

    /**
     * 版本号
     */
    @Column(name = "version", nullable = false, length = 20)
    private String version;

    /**
     * DSL内容
     */
    @Column(name = "dsl", nullable = false, columnDefinition = "LONGTEXT")
    private String dsl;

    /**
     * 版本描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 是否当前版本
     */
    @Column(name = "is_current")
    private Boolean isCurrent = false;
}
