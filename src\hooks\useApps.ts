import { ref, type Ref } from 'vue';
import { findLowCodeApps } from '@/apis';
import type { LowCodeAppVO } from '@/shared';
export function useApps() {
  const list: Ref<LowCodeAppVO[]> = ref([]);

  const load = () => {
    findLowCodeApps({ page: 1, limit: 9999 })
      .then((res) => {
        // 处理正常的成功响应
        const dataList = (res as any)?.data?.list || (res as any)?.list || [];
        const _list: LowCodeAppVO[] = dataList.map((item: any) => {
          return {
            ...item
          } as LowCodeAppVO;
        });
        list.value = _list;
      })
      .catch((error) => {
        // 处理特殊情况：createApi将成功响应当作错误处理
        if (error?.code === 200 && error?.data?.list) {
          const _list: LowCodeAppVO[] = error.data.list.map((item: any) => {
            return {
              ...item
            } as LowCodeAppVO;
          });
          list.value = _list;
        } else {
          console.error('Failed to load apps:', error);
          list.value = [];
        }
      });
  };

  load();

  return {
    list,
    load
  };
}
